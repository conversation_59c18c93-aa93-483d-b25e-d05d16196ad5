import{_ as t,v as e,o as a,c as s,w as i,h as n,A as l,n as o,j as r,F as c,t as u,i as h,l as D,ar as d}from"./index-BcNPWRTq.js";const m=t({name:"TimeFilter",props:{value:{type:String,default:"today"}},data(){return{activeFilter:this.value,showCustomInputs:!1,startDate:"",endDate:""}},watch:{value(t){this.activeFilter=t}},created(){const t=new Date,e=new Date;e.setMonth(e.getMonth()-1),this.startDate=this.formatDate(e),this.endDate=this.formatDate(t)},methods:{setFilter(t){this.activeFilter=t,"custom"!==t&&(this.showCustomInputs=!1);const e=this.getTimeRange(t),a={type:t,startDate:e.startDate,endDate:e.endDate};this.$emit("input",a),this.$emit("change",a)},toggleCustomDatePicker(){this.activeFilter="custom",this.showCustomInputs=!0},closeCustomInputs(){this.showCustomInputs=!1,this.activeFilter="today",this.setFilter("today")},onStartDateChange(t){this.startDate=t.detail.value,this.emitCustomDateRange()},onEndDateChange(t){this.endDate=t.detail.value,this.emitCustomDateRange()},emitCustomDateRange(){if(this.startDate&&this.endDate){if(new Date(this.startDate)>new Date(this.endDate))return void e({title:"开始日期不能大于结束日期",icon:"none"});const t={type:"custom",startDate:this.startDate,endDate:this.endDate};this.$emit("input",t),this.$emit("change",t)}},formatDate:t=>`${t.getFullYear()}-${(t.getMonth()+1).toString().padStart(2,"0")}-${t.getDate().toString().padStart(2,"0")}`,getTimeRange(t=this.activeFilter){switch(t){case"today":default:return{startDate:this.formatDate(new Date),endDate:this.formatDate(new Date)};case"yesterday":const t=new Date;return t.setDate(t.getDate()-1),{startDate:this.formatDate(t),endDate:this.formatDate(t)};case"thisWeek":const e=new Date,a=e.getDay()||7,s=new Date(e);return s.setDate(e.getDate()-a+1),{startDate:this.formatDate(s),endDate:this.formatDate(e)};case"thisMonth":const i=new Date,n=new Date(i.getFullYear(),i.getMonth(),1);return{startDate:this.formatDate(n),endDate:this.formatDate(i)};case"custom":return{startDate:this.startDate,endDate:this.endDate}}}}},[["render",function(t,e,m,f,g,v){const C=h,p=D,F=d;return a(),s(C,{class:"time-filter"},{default:i((()=>[n(C,{class:"filter-buttons"},{default:i((()=>[g.showCustomInputs?(a(),l(c,{key:1},[n(C,{class:"back-button",onClick:v.closeCustomInputs},{default:i((()=>[n(p,{class:"back-text"},{default:i((()=>[r("返回")])),_:1})])),_:1},8,["onClick"]),n(C,{class:"custom-inputs-container"},{default:i((()=>[n(C,{class:"date-input-item"},{default:i((()=>[n(F,{mode:"date",value:g.startDate,onChange:v.onStartDateChange},{default:i((()=>[n(C,{class:"date-input-inline"},{default:i((()=>[r(u(g.startDate||"开始日期"),1)])),_:1})])),_:1},8,["value","onChange"])])),_:1}),n(C,{class:"date-input-item"},{default:i((()=>[n(F,{mode:"date",value:g.endDate,onChange:v.onEndDateChange},{default:i((()=>[n(C,{class:"date-input-inline"},{default:i((()=>[r(u(g.endDate||"结束日期"),1)])),_:1})])),_:1},8,["value","onChange"])])),_:1})])),_:1})],64)):(a(),l(c,{key:0},[n(C,{class:o(["filter-item",{active:"today"===g.activeFilter}]),onClick:e[0]||(e[0]=t=>v.setFilter("today"))},{default:i((()=>[r("今日 ")])),_:1},8,["class"]),n(C,{class:o(["filter-item",{active:"yesterday"===g.activeFilter}]),onClick:e[1]||(e[1]=t=>v.setFilter("yesterday"))},{default:i((()=>[r("昨日")])),_:1},8,["class"]),n(C,{class:o(["filter-item",{active:"thisWeek"===g.activeFilter}]),onClick:e[2]||(e[2]=t=>v.setFilter("thisWeek"))},{default:i((()=>[r(" 本周")])),_:1},8,["class"]),n(C,{class:o(["filter-item",{active:"thisMonth"===g.activeFilter}]),onClick:e[3]||(e[3]=t=>v.setFilter("thisMonth"))},{default:i((()=>[r("本月")])),_:1},8,["class"]),n(C,{class:o(["filter-item",{active:"custom"===g.activeFilter}]),onClick:v.toggleCustomDatePicker},{default:i((()=>[r(" 自定义")])),_:1},8,["class","onClick"])],64))])),_:1})])),_:1})}],["__scopeId","data-v-6a476b5a"]]);export{m as T};

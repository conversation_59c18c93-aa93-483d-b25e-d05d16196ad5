/**
 * 微信用户认证服务
 * 专门处理微信用户的登录认证和用户信息管理
 */

// 导入API模块
import { wechatLogin as wechatLoginApi } from '../api/video-user.js'
import { wechatAuthCallback, formatWechatUserData } from '../api/wechat.js'

// 存储键名 - 与系统管理员完全分离
const WECHAT_STORAGE_KEYS = {
  USER_INFO: 'wechatUserInfo',
  USER_TOKEN: 'wechatUserToken'
}

/**
 * 微信用户认证服务类
 */
class WechatUserService {
  /**
   * 微信用户登录
   * @param {Object} loginData - 微信登录数据
   * @param {string} loginData.code - 微信授权码
   * @param {string} loginData.employeeId - 员工ID（可选）
   * @param {number} loginData.batchId - 批次ID（可选）
   * @returns {Promise<Object>} 登录结果
   */
  async wechatLogin (loginData) {
    try {
      const result = await wechatLoginApi(loginData)

      if (result.success && result.data) {
        const token = result.data.userToken || result.data.token

        // 检查用户审核状态
        if (result.data.auditStatus !== undefined && result.data.auditStatus !== 1) {
          const statusText = result.data.auditStatus === 0 ? '待审核' : '已拒绝'
          throw new Error(`用户审核状态：${statusText}，无法使用业务功能`)
        }

        // 只有审核通过的用户才保存信息
        this.saveUserInfo(result.data.userInfo)
        if (token) {
          this.saveUserToken(token)
        }

        return {
          success: true,
          message: '微信登录成功',
          data: {
            ...result.data,
            token: token
          }
        }
      } else {
        throw new Error(result.message || '微信登录失败')
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      return {
        success: false,
        message: error.message || '微信登录失败，请重试'
      }
    }
  }

  /**
   * 尝试微信自动登录
   * @param {Object} options - 页面参数
   * @returns {Promise<Object>} 登录结果
   */
  async tryWechatAutoLogin (options) {
    try {
      // H5端使用网页授权
      return await this.tryWechatWebAuth(options)
    } catch (error) {
      console.error('微信自动登录失败:', error)
      throw error
    }
  }

  /**
   * 尝试微信网页授权登录（H5端）
   * @param {Object} options - 页面参数
   * @returns {Promise<Object>} 登录结果
   */
  async tryWechatWebAuth (options) {
    try {
      // 导入网页授权工具
      const wechatWebAuth = await import('../utils/wechatWebAuth.js')
      const webAuth = wechatWebAuth.default

      // 检查环境
      if (!webAuth.isH5Environment()) {
        throw new Error('当前环境不支持微信网页授权')
      }

      // 启动授权流程
      const extraState = {
        batchId: options.batchId ? parseInt(options.batchId) : null,
        returnUrl: options.returnUrl || '/pages/index/index'
      }

      await webAuth.startAuth({
        extraState,
        inviterId: options.sharerId || options.employeeId || null
      })
      // 注意：这里会跳转到微信授权页面，不会返回结果
      // 实际的登录处理在回调页面中完成
      return {
        success: true,
        message: '正在跳转到微信授权页面...'
      }
    } catch (error) {
      console.error('微信网页授权失败:', error)
      throw error
    }
  }

  /**
   * H5端微信登录（使用授权码）
   * @param {Object} callbackData - 回调数据
   * @param {string} callbackData.Code - 微信授权码
   * @param {string} callbackData.State - 状态参数
   * @param {string} callbackData.InviterId - 邀请人ID（可选）
   * @returns {Promise<Object>} 登录结果
   */
  async wechatWebLogin (callbackData) {
    try {
      const { Code, State, InviterId } = callbackData

      // 调用后端回调接口
      const result = await wechatAuthCallback({
        Code: Code,
        State: State,
        InviterId: InviterId || ''
      })

      if (result.success && result.data) {
        // 格式化用户数据
        const formattedUserInfo = formatWechatUserData(result.data.userInfo || result.data.user)
        const token = result.data.userToken || result.data.token

        // 只有审核通过的用户才缓存信息
        if (result.data.auditStatus === 1) {
          console.log('用户已审核通过，缓存用户信息')
          this.saveUserInfo(formattedUserInfo)
          if (token) {
            this.saveUserToken(token)
          }
        } else {
          console.log('用户未审核通过，不缓存用户信息，状态:', result.data.auditStatus)
          // 清除可能存在的旧缓存
          this.clearUserInfo()
          this.clearUserToken()
        }

        return {
          success: true,
          message: '微信网页登录成功',
          data: {
            userInfo: formattedUserInfo,
            token: token,
            auditStatus: result.data.auditStatus,
            isNewUser: result.data.isNewUser
          }
        }
      } else {
        throw new Error(result.msg || result.message || '微信网页登录失败')
      }
    } catch (error) {
      console.error('微信网页登录失败:', error)
      return {
        success: false,
        message: error.message || '微信网页登录失败，请重试'
      }
    }
  }

  /**
   * 保存微信用户信息到存储
   * @param {Object} userInfo - 用户信息
   */
  saveUserInfo (userInfo) {
    try {
      uni.setStorageSync(WECHAT_STORAGE_KEYS.USER_INFO, userInfo)
    } catch (error) {
      console.error('Error saving wechat user info:', error)
    }
  }

  /**
   * 保存微信用户token到存储
   * @param {string} token - 用户token
   */
  saveUserToken (token) {
    try {
      uni.setStorageSync(WECHAT_STORAGE_KEYS.USER_TOKEN, token)
    } catch (error) {
      console.error('Error saving wechat user token:', error)
    }
  }

  /**
   * 获取当前微信用户信息
   * @returns {Object|null}
   */
  getUserInfo () {
    try {
      return uni.getStorageSync(WECHAT_STORAGE_KEYS.USER_INFO) || null
    } catch (error) {
      console.error('Error getting wechat user info:', error)
      return null
    }
  }

  /**
   * 获取当前微信用户token
   * @returns {string|null}
   */
  getUserToken () {
    try {
      return uni.getStorageSync(WECHAT_STORAGE_KEYS.USER_TOKEN) || null
    } catch (error) {
      console.error('Error getting wechat user token:', error)
      return null
    }
  }

  /**
   * 检查微信用户是否已登录
   * @returns {boolean}
   */
  isLoggedIn () {
    const userInfo = this.getUserInfo()
    const token = this.getUserToken()
    return !!(userInfo && token)
  }

  /**
   * 获取微信用户ID
   * @returns {string|null}
   */
  getUserId () {
    const userInfo = this.getUserInfo()
    return userInfo?.id ?? null
  }

  /**
   * 获取微信用户昵称
   * @returns {string|null}
   */
  getNickname () {
    const userInfo = this.getUserInfo()
    return userInfo?.nickname ?? null
  }

  /**
   * 获取微信用户头像
   * @returns {string|null}
   */
  getAvatar () {
    const userInfo = this.getUserInfo()
    return userInfo?.avatar ?? null
  }

  /**
   * 获取微信用户OpenID
   * @returns {string|null}
   */
  getOpenId () {
    const userInfo = this.getUserInfo()
    return userInfo?.openId ?? null
  }

  /**
   * 获取绑定的员工ID
   * @returns {string|null}
   */
  getEmployeeId () {
    const userInfo = this.getUserInfo()
    return userInfo?.employeeId ?? null
  }

  /**
   * 微信用户登出
   */
  logout () {
    try {
      // 清理本地存储
      uni.removeStorageSync(WECHAT_STORAGE_KEYS.USER_INFO)
      uni.removeStorageSync(WECHAT_STORAGE_KEYS.USER_TOKEN)
      
      console.log('微信用户已登出')
      return true
    } catch (error) {
      console.error('Error during wechat user logout:', error)
      return false
    }
  }

  /**
   * 获取微信用户显示信息
   * @returns {Object}
   */
  getDisplayInfo () {
    const userInfo = this.getUserInfo()
    
    if (!userInfo) {
      return {
        nickname: '未登录',
        avatar: '/static/logo.png',
        userType: 'guest'
      }
    }

    return {
      id: userInfo.id,
      nickname: userInfo.nickname || '微信用户',
      avatar: userInfo.avatar || '/static/logo.png',
      openId: userInfo.openId,
      employeeId: userInfo.employeeId,
      userType: 'wechat_user',
      createTime: userInfo.createTime,
      lastLogin: userInfo.lastLogin
    }
  }



  /**
   * 跳转到微信用户登录页面（如果需要）
   * @param {string} returnUrl - 登录成功后返回的URL
   */
  redirectToLogin (returnUrl = '') {
    const url = returnUrl 
      ? `/pages/user-login/index?returnUrl=${encodeURIComponent(returnUrl)}`
      : '/pages/user-login/index'
    
    uni.redirectTo({ url })
  }

  /**
   * 检查是否需要微信用户认证
   * @returns {boolean} 是否需要认证
   */
  requireAuth () {
    if (!this.isLoggedIn()) {
      console.log('微信用户未登录，需要认证')
      return true
    }
    return false
  }

  /**
   * 清除用户信息
   */
  clearUserInfo () {
    try {
      uni.removeStorageSync(WECHAT_STORAGE_KEYS.USER_INFO)
      console.log('已清除微信用户信息')
    } catch (error) {
      console.error('清除用户信息失败:', error)
    }
  }

  /**
   * 清除用户Token
   */
  clearUserToken () {
    try {
      uni.removeStorageSync(WECHAT_STORAGE_KEYS.USER_TOKEN)
      console.log('已清除微信用户Token')
    } catch (error) {
      console.error('清除用户Token失败:', error)
    }
  }

  /**
   * 清除所有微信用户数据
   */
  clearAll () {
    this.clearUserInfo()
    this.clearUserToken()
    console.log('已清除所有微信用户数据')
  }
}

// 创建单例实例
const wechatUserService = new WechatUserService()

// 导出服务实例和工具函数
export default wechatUserService
export { WechatUserService, WECHAT_STORAGE_KEYS }

// 导出常用函数以便使用
export const getWechatUserInfo = () => wechatUserService.getUserInfo()
export const getWechatUserToken = () => wechatUserService.getUserToken()
export const isWechatUserLoggedIn = () => wechatUserService.isLoggedIn()
export const getWechatUserId = () => wechatUserService.getUserId()
export const getWechatUserNickname = () => wechatUserService.getNickname()
export const wechatWebLogin = (loginData) => wechatUserService.wechatWebLogin(loginData)
export const tryWechatWebAuth = (options) => wechatUserService.tryWechatWebAuth(options)

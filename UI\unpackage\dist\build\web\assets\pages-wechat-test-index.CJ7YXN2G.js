import{_ as s,a4 as e,J as t,K as a,r as o,b as i,c as n,w as d,i as l,f as c,g as r,o as g,h,j as f,t as u,k as L,n as w,A as m,B as p,F as b,l as _,a5 as y}from"./index-BcNPWRTq.js";import $,{setLogCallback as U}from"./utils-wechatWebAuth.CHzwRabY.js";import"./wechat.BxyZ_GJG.js";const k=s({data:()=>({isLoading:!1,userInfo:null,loginTime:"",platformInfo:{platform:"",isH5:!1,isWechatBrowser:!1,userAgent:""},wechatConfig:{appId:"",scope:"",redirectUri:""},logs:[],authLinkUrl:"",debugInfo:null}),onLoad(s){if(window.addEventListener("error",(s=>{var e;this.addLog(`全局错误: ${(null==(e=s.error)?void 0:e.message)||s.message}`,"error"),console.error("全局错误事件:",s)})),window.addEventListener("unhandledrejection",(s=>{var e;this.addLog(`未处理的Promise拒绝: ${(null==(e=s.reason)?void 0:e.message)||s.reason}`,"error"),console.error("未处理的Promise拒绝:",s)})),U(((s,e)=>{this.addLog(s,e)})),s.debug)try{this.debugInfo=JSON.parse(decodeURIComponent(s.debug)),this.addLog("收到微信授权回调调试信息","success"),this.addLog(`调试信息: ${JSON.stringify(this.debugInfo)}`,"info")}catch(e){this.addLog("解析调试信息失败: "+e.message,"error")}this.initPage()},methods:{initPage(){this.addLog("页面初始化开始","info"),this.getPlatformInfo(),this.getWechatConfig(),this.checkLoginStatus(),this.addLog("页面初始化完成","success")},getPlatformInfo(){try{const s=e();this.platformInfo={platform:s.platform||"unknown",isH5:$.isH5Environment(),isWechatBrowser:$.isWechatBrowser(),userAgent:"undefined"!=typeof window?window.navigator.userAgent:"N/A"},this.addLog(`平台信息获取成功: ${this.platformInfo.platform}`,"info")}catch(s){this.addLog(`获取平台信息失败: ${s.message}`,"error")}},getWechatConfig(){try{const s=$.validateConfig();s.valid?(this.wechatConfig={appId:$.config.appId,scope:$.config.scope,redirectUri:$.getRedirectUri()},this.addLog("微信配置加载成功","success")):this.addLog(`微信配置验证失败: ${s.errors.join(", ")}`,"error")}catch(s){this.addLog(`获取微信配置失败: ${s.message}`,"error")}},checkLoginStatus(){try{const s=t("wechatUserInfo"),e=t("wechatUserToken");s&&e?(this.userInfo=s,this.loginTime=(new Date).toLocaleString(),this.addLog("发现已登录用户信息","success")):this.addLog("未发现登录信息","info")}catch(s){this.addLog(`检查登录状态失败: ${s.message}`,"error")}},async handleWechatLogin(){try{if(this.isLoading=!0,this.addLog("=== 开始微信登录流程 ===","info"),this.addLog(`window对象存在: ${"undefined"!=typeof window}`,"info"),"undefined"!=typeof window&&this.addLog(`当前URL: ${window.location.href}`,"info"),this.addLog(`wechatWebAuth对象存在: ${!!$}`,"info"),this.addLog("wechatWebAuth类型: "+typeof $,"info"),this.addLog(`当前环境检查: isH5=${this.platformInfo.isH5}, platform=${this.platformInfo.platform}`,"info"),!this.platformInfo.isH5)throw new Error("当前环境不支持网页端微信登录");this.addLog("检查微信配置...","info"),this.addLog(`wechatWebAuth.config: ${JSON.stringify($.config)}`,"info");const s=$.validateConfig();if(this.addLog(`配置验证结果: ${JSON.stringify(s)}`,"info"),!s.valid)throw this.addLog(`微信配置验证失败: ${s.errors.join(", ")}`,"error"),new Error(`微信配置错误: ${s.errors.join(", ")}`);this.addLog("微信配置验证通过","success");const e={returnUrl:"/pages/wechat-test/index",timestamp:Date.now()};this.addLog(`生成状态参数: ${JSON.stringify(e)}`,"info");const t=$.getRedirectUri();this.addLog(`回调地址: ${t}`,"info"),this.addLog(`startAuth方法存在: ${"function"==typeof $.startAuth}`,"info"),this.addLog("准备调用startAuth方法","info");const a={extraState:e,useDirect:!0};this.addLog(`startAuth参数: ${JSON.stringify(a)}`,"info"),await $.startAuth(a),this.addLog("startAuth方法调用完成","success")}catch(s){this.addLog(`微信登录失败: ${s.message}`,"error"),this.addLog(`错误名称: ${s.name}`,"error"),this.addLog(`错误堆栈: ${s.stack}`,"error"),this.showToastMessage(s.message,"error"),console.error("微信登录详细错误:",s)}finally{this.isLoading=!1,this.addLog("=== 微信登录流程结束 ===","info")}},async testAuthUrl(){try{this.addLog("开始测试授权URL生成（后端接口）","info");const s=await $.buildAuthUrl({state:$.generateState({test:!0})});this.addLog(`生成的授权URL: ${s}`,"success"),this.showToastMessage("授权URL已生成，请查看日志","success"),navigator.clipboard&&navigator.clipboard.writeText(s).then((()=>{this.addLog("授权URL已复制到剪贴板","success")})).catch((()=>{this.addLog("复制到剪贴板失败","warning")}))}catch(s){this.addLog(`生成授权URL失败: ${s.message}`,"error"),this.showToastMessage(s.message,"error")}},testAuthUrlDirect(){try{this.addLog("开始测试授权URL生成（直接构造）","info");const s=$.buildAuthUrlDirect({state:$.generateState({test:!0})});this.addLog(`生成的授权URL: ${s}`,"success"),this.showToastMessage("授权URL已生成，请查看日志","success"),navigator.clipboard&&navigator.clipboard.writeText(s).then((()=>{this.addLog("授权URL已复制到剪贴板","success")})).catch((()=>{this.addLog("复制到剪贴板失败","warning")}))}catch(s){this.addLog(`生成授权URL失败: ${s.message}`,"error"),this.showToastMessage(s.message,"error")}},testDirectJump(){try{this.addLog("开始测试直接跳转","info");const a=$.buildAuthUrlDirect({state:$.generateState({test:!0})});this.addLog(`生成的授权URL: ${a}`,"success"),this.addLog("执行直接跳转...","info"),this.addLog(`跳转前URL: ${window.location.href}`,"info");try{window.location.href=a,this.addLog("方式1: window.location.href 已执行","info")}catch(s){this.addLog(`方式1失败: ${s.message}`,"error");try{window.location.replace(a),this.addLog("方式2: window.location.replace 已执行","info")}catch(e){this.addLog(`方式2失败: ${e.message}`,"error");try{window.location.assign(a),this.addLog("方式3: window.location.assign 已执行","info")}catch(t){this.addLog(`方式3失败: ${t.message}`,"error")}}}setTimeout((()=>{this.addLog(`跳转后URL: ${window.location.href}`,"info"),window.location.href===a||window.location.href.includes("open.weixin.qq.com")?this.addLog("跳转成功！","success"):this.addLog("跳转可能失败，URL未改变","warning")}),500)}catch(a){this.addLog(`测试直接跳转失败: ${a.message}`,"error"),this.showToastMessage(a.message,"error")}},testForceJump(){try{this.addLog("开始强制跳转测试","info");const e=$.buildAuthUrlDirect({state:$.generateState({test:!0})});this.addLog(`目标URL: ${e}`,"info"),this.addLog(`当前URL: ${window.location.href}`,"info"),this.addLog(`是否微信浏览器: ${$.isWechatBrowser()}`,"info"),this.addLog("尝试方式1: a标签点击","info");try{const s=document.createElement("a");s.href=e,s.target="_self",s.style.display="none",document.body.appendChild(s);const t=new MouseEvent("click",{view:window,bubbles:!0,cancelable:!0});s.dispatchEvent(t),document.body.removeChild(s),this.addLog("方式1: a标签点击已执行","success"),setTimeout((()=>{this.addLog(`方式1后URL: ${window.location.href}`,"info")}),200)}catch(s){this.addLog(`方式1失败: ${s.message}`,"error")}setTimeout((()=>{if(!window.location.href.includes("open.weixin.qq.com")){this.addLog("方式1未成功，尝试方式2: location.replace","info");try{window.location.replace(e),this.addLog("方式2: location.replace 已执行","success")}catch(s){this.addLog(`方式2失败: ${s.message}`,"error"),setTimeout((()=>{if(!window.location.href.includes("open.weixin.qq.com")){this.addLog("方式2未成功，尝试方式3: top.location","info");try{window.top&&window.top!==window?(window.top.location.href=e,this.addLog("方式3: top.location 已执行","success")):(this.addLog("方式3: 当前就是顶层窗口","info"),window.parent&&window.parent!==window?(window.parent.location.href=e,this.addLog("方式4: parent.location 已执行","success")):(this.addLog("方式4: 当前就是父窗口","info"),window.location.href=e,this.addLog("最后尝试: 直接赋值已执行","info")))}catch(s){this.addLog(`方式3失败: ${s.message}`,"error")}}}),300)}}}),300),setTimeout((()=>{this.addLog(`最终URL: ${window.location.href}`,"info"),window.location.href.includes("open.weixin.qq.com")?this.addLog("强制跳转成功！","success"):(this.addLog("所有跳转方式都失败了","error"),this.addLog("可能是微信浏览器的安全限制","warning"))}),1e3)}catch(e){this.addLog(`强制跳转测试失败: ${e.message}`,"error"),this.showToastMessage(e.message,"error")}},generateClickableLink(){try{this.addLog("生成可点击的授权链接","info");const s=$.buildAuthUrlDirect({state:$.generateState({returnUrl:"/pages/wechat-test/index",timestamp:Date.now()})});this.authLinkUrl=s,this.addLog(`授权链接已生成: ${s}`,"success"),this.addLog("请点击下方的蓝色按钮进行授权","info")}catch(s){this.addLog(`生成授权链接失败: ${s.message}`,"error"),this.showToastMessage(s.message,"error")}},openAuthLink(){try{if(!this.authLinkUrl)return void this.showToastMessage("请先生成授权链接","warning");this.addLog("用户点击授权链接","info"),this.addLog(`即将跳转到: ${this.authLinkUrl}`,"info"),window.location.href=this.authLinkUrl}catch(s){this.addLog(`打开授权链接失败: ${s.message}`,"error"),this.showToastMessage(s.message,"error")}},async validateAuthUrl(){try{this.addLog("开始验证授权URL","info");const e=$.buildAuthUrlDirect({state:$.generateState({test:!0})});this.addLog(`验证URL: ${e}`,"info");try{const s=new URL(e);this.addLog(`URL协议: ${s.protocol}`,"info"),this.addLog(`URL主机: ${s.host}`,"info"),this.addLog(`URL路径: ${s.pathname}`,"info"),this.addLog(`URL参数: ${s.search}`,"info"),this.addLog(`URL哈希: ${s.hash}`,"info");const t=new URLSearchParams(s.search),a=["appid","redirect_uri","response_type","scope","state"];for(const e of a){const s=t.get(e);s?this.addLog(`参数 ${e}: ${s}`,"success"):this.addLog(`缺少参数 ${e}`,"error")}this.addLog("URL格式验证通过","success")}catch(s){this.addLog(`URL格式错误: ${s.message}`,"error")}this.addLog("请复制上面的URL到新标签页手动打开，查看是否能正常访问微信授权页面","info"),this.addLog('如果显示"参数错误"或"域名未配置"，说明需要在微信公众平台配置域名',"warning")}catch(e){this.addLog(`验证授权URL失败: ${e.message}`,"error"),this.showToastMessage(e.message,"error")}},async forceJumpToAuth(){try{this.addLog("=== 强制跳转到微信授权（最新版）===","info");const s=$.buildAuthUrlDirect({state:$.generateState({returnUrl:"/pages/wechat-test/index",timestamp:Date.now()})});this.addLog(`生成授权URL: ${s}`,"success"),this.addLog(`当前页面: ${window.location.href}`,"info"),this.addLog("开始强制跳转...","info"),window.location.href=s,setTimeout((()=>{if(window.location.href.includes("wechat-test")){this.addLog("跳转可能失败，尝试其他方式...","warning");try{window.location.replace(s)}catch(e){this.addLog(`replace方式也失败: ${e.message}`,"error");try{window.open(s,"_self")}catch(t){this.addLog(`open方式也失败: ${t.message}`,"error"),this.addLog("所有跳转方式都失败，可能是微信浏览器限制","error")}}}}),3e3)}catch(s){this.addLog(`强制跳转失败: ${s.message}`,"error"),this.showToastMessage(s.message,"error")}},clearDebugInfo(){this.debugInfo=null,this.addLog("调试信息已清除","info")},handleLogout(){try{a("wechatUserInfo"),a("wechatUserToken"),this.userInfo=null,this.loginTime="",this.addLog("退出登录成功","success"),this.showToastMessage("已退出登录","success")}catch(s){this.addLog(`退出登录失败: ${s.message}`,"error"),this.showToastMessage(s.message,"error")}},refreshUserInfo(){this.checkLoginStatus(),this.showToastMessage("用户信息已刷新","success")},addLog(s,e="info"){const t={time:(new Date).toLocaleTimeString(),message:s,type:e};this.logs.unshift(t),this.logs.length>50&&(this.logs=this.logs.slice(0,50)),console.log(`[${e.toUpperCase()}] ${s}`)},async copyAllLogs(){try{if(0===this.logs.length)return void this.showToastMessage("暂无日志可复制","warning");const s=this.logs.map((s=>`[${s.time}] [${s.type.toUpperCase()}] ${s.message}`)).reverse().join("\n"),e=`\n=== 环境信息 ===\n平台: ${this.platformInfo.platform}\n是否H5: ${this.platformInfo.isH5}\n是否微信浏览器: ${this.platformInfo.isWechatBrowser}\n用户代理: ${this.platformInfo.userAgent}\n\n=== 微信配置 ===\nAppID: ${this.wechatConfig.appId}\n授权范围: ${this.wechatConfig.scope}\n回调地址: ${this.wechatConfig.redirectUri}\n\n=== 日志记录 ===\n${s}\n`;if(navigator.clipboard)await navigator.clipboard.writeText(e),this.showToastMessage("所有日志已复制到剪贴板","success"),this.addLog("日志已复制到剪贴板","success");else{const s=document.createElement("textarea");s.value=e,document.body.appendChild(s),s.select(),document.execCommand("copy"),document.body.removeChild(s),this.showToastMessage("所有日志已复制到剪贴板","success"),this.addLog("日志已复制到剪贴板（备用方案）","success")}}catch(s){console.error("复制日志失败:",s),this.addLog(`复制日志失败: ${s.message}`,"error"),this.showToastMessage("复制失败: "+s.message,"error")}},clearLogs(){this.logs=[],this.showToastMessage("日志已清空","success")},showToastMessage(s,e="success"){this.$refs.uToast.show({message:s,type:e,duration:3e3})}}},[["render",function(s,e,t,a,$,U){const k=_,I=l,C=o(i("u-button"),c),v=y,T=o(i("u-toast"),r);return g(),n(I,{class:"test-container"},{default:d((()=>[h(I,{class:"test-content"},{default:d((()=>[h(I,{class:"header-section"},{default:d((()=>[h(k,{class:"page-title"},{default:d((()=>[f("微信登录测试")])),_:1}),h(k,{class:"page-subtitle"},{default:d((()=>[f("网页端微信授权登录功能测试")])),_:1})])),_:1}),h(I,{class:"info-section"},{default:d((()=>[h(I,{class:"info-card"},{default:d((()=>[h(k,{class:"info-title"},{default:d((()=>[f("当前环境信息")])),_:1}),h(I,{class:"info-item"},{default:d((()=>[h(k,{class:"info-label"},{default:d((()=>[f("平台：")])),_:1}),h(k,{class:"info-value"},{default:d((()=>[f(u($.platformInfo.platform),1)])),_:1})])),_:1}),h(I,{class:"info-item"},{default:d((()=>[h(k,{class:"info-label"},{default:d((()=>[f("是否H5：")])),_:1}),h(k,{class:"info-value"},{default:d((()=>[f(u($.platformInfo.isH5?"是":"否"),1)])),_:1})])),_:1}),h(I,{class:"info-item"},{default:d((()=>[h(k,{class:"info-label"},{default:d((()=>[f("是否微信浏览器：")])),_:1}),h(k,{class:"info-value"},{default:d((()=>[f(u($.platformInfo.isWechatBrowser?"是":"否"),1)])),_:1})])),_:1}),h(I,{class:"info-item"},{default:d((()=>[h(k,{class:"info-label"},{default:d((()=>[f("用户代理：")])),_:1}),h(k,{class:"info-value small"},{default:d((()=>[f(u($.platformInfo.userAgent),1)])),_:1})])),_:1})])),_:1})])),_:1}),h(I,{class:"config-section"},{default:d((()=>[h(I,{class:"config-card"},{default:d((()=>[h(k,{class:"config-title"},{default:d((()=>[f("微信配置信息")])),_:1}),h(I,{class:"config-item"},{default:d((()=>[h(k,{class:"config-label"},{default:d((()=>[f("AppID：")])),_:1}),h(k,{class:"config-value"},{default:d((()=>[f(u($.wechatConfig.appId),1)])),_:1})])),_:1}),h(I,{class:"config-item"},{default:d((()=>[h(k,{class:"config-label"},{default:d((()=>[f("授权范围：")])),_:1}),h(k,{class:"config-value"},{default:d((()=>[f(u($.wechatConfig.scope),1)])),_:1})])),_:1}),h(I,{class:"config-item"},{default:d((()=>[h(k,{class:"config-label"},{default:d((()=>[f("回调地址：")])),_:1}),h(k,{class:"config-value small"},{default:d((()=>[f(u($.wechatConfig.redirectUri),1)])),_:1})])),_:1})])),_:1})])),_:1}),h(I,{class:"status-section"},{default:d((()=>[h(I,{class:"status-card"},{default:d((()=>[h(k,{class:"status-title"},{default:d((()=>[f("登录状态")])),_:1}),$.userInfo?(g(),n(I,{key:0,class:"user-info"},{default:d((()=>[h(I,{class:"status-item"},{default:d((()=>[h(k,{class:"status-label"},{default:d((()=>[f("用户昵称：")])),_:1}),h(k,{class:"status-value"},{default:d((()=>[f(u($.userInfo.nickname||"未知"),1)])),_:1})])),_:1}),h(I,{class:"status-item"},{default:d((()=>[h(k,{class:"status-label"},{default:d((()=>[f("用户ID：")])),_:1}),h(k,{class:"status-value"},{default:d((()=>[f(u($.userInfo.id||"未知"),1)])),_:1})])),_:1}),h(I,{class:"status-item"},{default:d((()=>[h(k,{class:"status-label"},{default:d((()=>[f("登录时间：")])),_:1}),h(k,{class:"status-value"},{default:d((()=>[f(u($.loginTime),1)])),_:1})])),_:1})])),_:1})):(g(),n(I,{key:1,class:"no-user"},{default:d((()=>[h(k,{class:"no-user-text"},{default:d((()=>[f("未登录")])),_:1})])),_:1}))])),_:1})])),_:1}),h(I,{class:"action-section"},{default:d((()=>[$.userInfo?(g(),n(I,{key:1,class:"logged-actions"},{default:d((()=>[h(C,{type:"warning",onClick:U.handleLogout,class:"logout-btn"},{default:d((()=>[f(" 退出登录 ")])),_:1},8,["onClick"]),h(C,{type:"info",onClick:U.refreshUserInfo,class:"refresh-btn"},{default:d((()=>[f(" 刷新信息 ")])),_:1},8,["onClick"])])),_:1})):(g(),n(C,{key:0,type:"success",onClick:U.handleWechatLogin,disabled:$.isLoading,loading:$.isLoading,icon:"weixin",class:"login-btn"},{default:d((()=>[f(u($.isLoading?"正在跳转...":"微信登录"),1)])),_:1},8,["onClick","disabled","loading"])),h(C,{type:"primary",onClick:U.testAuthUrl,class:"test-btn"},{default:d((()=>[f(" 测试授权URL生成（后端接口） ")])),_:1},8,["onClick"]),h(C,{type:"default",onClick:U.testAuthUrlDirect,class:"test-btn"},{default:d((()=>[f(" 测试授权URL生成（直接构造） ")])),_:1},8,["onClick"]),h(C,{type:"warning",onClick:U.testDirectJump,class:"test-btn"},{default:d((()=>[f(" 测试直接跳转到授权页面 ")])),_:1},8,["onClick"]),h(C,{type:"error",onClick:U.testForceJump,class:"test-btn"},{default:d((()=>[f(" 强制跳转测试（多种方式） ")])),_:1},8,["onClick"]),h(C,{type:"success",onClick:U.generateClickableLink,class:"test-btn"},{default:d((()=>[f(" 生成可点击的授权链接 ")])),_:1},8,["onClick"]),h(C,{type:"info",onClick:U.validateAuthUrl,class:"test-btn"},{default:d((()=>[f(" 验证授权URL是否可访问 ")])),_:1},8,["onClick"]),h(C,{type:"primary",onClick:U.forceJumpToAuth,class:"test-btn",style:{background:"#ff6b35"}},{default:d((()=>[f(" 🚀 强制跳转到微信授权（最新版） ")])),_:1},8,["onClick"]),$.authLinkUrl?(g(),n(I,{key:2,class:"auth-link-container"},{default:d((()=>[h(k,{class:"auth-link-title"},{default:d((()=>[f("请点击下面的链接进行微信授权：")])),_:1}),h(I,{class:"auth-link-wrapper"},{default:d((()=>[h(k,{class:"auth-link",onClick:U.openAuthLink},{default:d((()=>[f(u($.authLinkUrl),1)])),_:1},8,["onClick"])])),_:1}),h(C,{type:"primary",onClick:U.openAuthLink,class:"auth-link-btn"},{default:d((()=>[f(" 点击进行微信授权 ")])),_:1},8,["onClick"])])),_:1})):L("",!0),$.debugInfo?(g(),n(I,{key:3,class:"debug-info-container"},{default:d((()=>[h(k,{class:"debug-info-title"},{default:d((()=>[f("🔍 微信授权回调调试信息")])),_:1}),h(I,{class:"debug-info-content"},{default:d((()=>[h(I,{class:"debug-item"},{default:d((()=>[h(k,{class:"debug-label"},{default:d((()=>[f("授权状态:")])),_:1}),h(k,{class:w(["debug-value",$.debugInfo.success?"success":"error"])},{default:d((()=>[f(u($.debugInfo.success?"✅ 成功":"❌ 失败"),1)])),_:1},8,["class"])])),_:1}),$.debugInfo.code?(g(),n(I,{key:0,class:"debug-item"},{default:d((()=>[h(k,{class:"debug-label"},{default:d((()=>[f("授权码(code):")])),_:1}),h(k,{class:"debug-value code"},{default:d((()=>[f(u($.debugInfo.code),1)])),_:1})])),_:1})):L("",!0),$.debugInfo.state?(g(),n(I,{key:1,class:"debug-item"},{default:d((()=>[h(k,{class:"debug-label"},{default:d((()=>[f("状态参数(state):")])),_:1}),h(k,{class:"debug-value"},{default:d((()=>[f(u($.debugInfo.state),1)])),_:1})])),_:1})):L("",!0),$.debugInfo.stateData?(g(),n(I,{key:2,class:"debug-item"},{default:d((()=>[h(k,{class:"debug-label"},{default:d((()=>[f("状态数据:")])),_:1}),h(k,{class:"debug-value"},{default:d((()=>[f(u(JSON.stringify($.debugInfo.stateData)),1)])),_:1})])),_:1})):L("",!0),$.debugInfo.error?(g(),n(I,{key:3,class:"debug-item"},{default:d((()=>[h(k,{class:"debug-label"},{default:d((()=>[f("错误信息:")])),_:1}),h(k,{class:"debug-value error"},{default:d((()=>[f(u($.debugInfo.error),1)])),_:1})])),_:1})):L("",!0),$.debugInfo.message?(g(),n(I,{key:4,class:"debug-item"},{default:d((()=>[h(k,{class:"debug-label"},{default:d((()=>[f("详细信息:")])),_:1}),h(k,{class:"debug-value"},{default:d((()=>[f(u($.debugInfo.message),1)])),_:1})])),_:1})):L("",!0),h(I,{class:"debug-item"},{default:d((()=>[h(k,{class:"debug-label"},{default:d((()=>[f("回调时间:")])),_:1}),h(k,{class:"debug-value"},{default:d((()=>[f(u($.debugInfo.timestamp),1)])),_:1})])),_:1})])),_:1}),h(C,{type:"warning",onClick:U.clearDebugInfo,class:"clear-debug-btn"},{default:d((()=>[f(" 清除调试信息 ")])),_:1},8,["onClick"])])),_:1})):L("",!0)])),_:1}),h(I,{class:"log-section"},{default:d((()=>[h(I,{class:"log-card"},{default:d((()=>[h(I,{class:"log-header"},{default:d((()=>[h(k,{class:"log-title"},{default:d((()=>[f("测试日志")])),_:1}),h(I,{class:"log-actions"},{default:d((()=>[h(C,{type:"primary",size:"mini",onClick:U.copyAllLogs},{default:d((()=>[f("复制全部")])),_:1},8,["onClick"]),h(C,{type:"default",size:"mini",onClick:U.clearLogs},{default:d((()=>[f("清空")])),_:1},8,["onClick"])])),_:1})])),_:1}),h(v,{class:"log-content","scroll-y":""},{default:d((()=>[(g(!0),m(b,null,p($.logs,((s,e)=>(g(),n(I,{key:e,class:"log-item"},{default:d((()=>[h(k,{class:"log-time"},{default:d((()=>[f(u(s.time),1)])),_:2},1024),h(k,{class:w(["log-message",s.type])},{default:d((()=>[f(u(s.message),1)])),_:2},1032,["class"])])),_:2},1024)))),128)),0===$.logs.length?(g(),n(I,{key:0,class:"no-logs"},{default:d((()=>[h(k,{class:"no-logs-text"},{default:d((()=>[f("暂无日志")])),_:1})])),_:1})):L("",!0)])),_:1})])),_:1})])),_:1})])),_:1}),h(T,{ref:"uToast"},null,512)])),_:1})}],["__scopeId","data-v-27b8ab39"]]);export{k as default};

import{_ as s,v as a,a0 as o,a1 as e,T as t,x as l,c as d,w as r,i,o as n,h as c,j as f,t as m,l as u,a2 as w,C as P}from"./index-BcNPWRTq.js";import{P as p}from"./PageHeader.bWDLXl4H.js";import{c as h}from"./sysuser.CM5sttww.js";const _=s({components:{PageHeader:p},data:()=>({form:{oldPassword:"",newPassword:"",confirmPassword:""},loading:!1}),methods:{validatePassword:s=>!s||s.length<6?"密码长度不能少于6位":/(?=.*[a-zA-Z])(?=.*\d)/.test(s)?null:"密码必须包含字母和数字",async changePassword(){if(!this.form.oldPassword)return void a({title:"请输入当前密码",icon:"none"});if(!this.form.newPassword)return void a({title:"请输入新密码",icon:"none"});if(this.form.newPassword!==this.form.confirmPassword)return void a({title:"两次输入的密码不一致",icon:"none"});const s=this.validatePassword(this.form.newPassword);if(s)a({title:s,icon:"none"});else if(this.form.oldPassword!==this.form.newPassword)try{this.loading=!0;const s=o();if(!s||!s.userId)return void a({title:"用户信息获取失败",icon:"none"});const l=e.MD5(this.form.oldPassword).toString(),d=e.MD5(this.form.newPassword).toString();await h({userId:s.userId,oldPassword:l,newPassword:d}),a({title:"密码修改成功",icon:"success"}),this.form={oldPassword:"",newPassword:"",confirmPassword:""},setTimeout((()=>{t()}),1500)}catch(l){a({title:l.message||"密码修改失败",icon:"none"})}finally{this.loading=!1}else a({title:"新密码不能与当前密码相同",icon:"none"})}}},[["render",function(s,a,o,e,t,p){const h=l("PageHeader"),_=u,g=w,V=i,b=P;return n(),d(V,{class:"container"},{default:r((()=>[c(h,{title:"修改密码"}),c(V,{class:"form-container"},{default:r((()=>[c(V,{class:"form-section"},{default:r((()=>[c(V,{class:"form-item"},{default:r((()=>[c(_,{class:"label"},{default:r((()=>[f("当前密码")])),_:1}),c(g,{type:"password",modelValue:t.form.oldPassword,"onUpdate:modelValue":a[0]||(a[0]=s=>t.form.oldPassword=s),placeholder:"请输入当前密码",class:"input"},null,8,["modelValue"])])),_:1}),c(V,{class:"form-item"},{default:r((()=>[c(_,{class:"label"},{default:r((()=>[f("新密码")])),_:1}),c(g,{type:"password",modelValue:t.form.newPassword,"onUpdate:modelValue":a[1]||(a[1]=s=>t.form.newPassword=s),placeholder:"请输入新密码",class:"input"},null,8,["modelValue"])])),_:1}),c(V,{class:"form-item"},{default:r((()=>[c(_,{class:"label"},{default:r((()=>[f("确认新密码")])),_:1}),c(g,{type:"password",modelValue:t.form.confirmPassword,"onUpdate:modelValue":a[2]||(a[2]=s=>t.form.confirmPassword=s),placeholder:"请再次输入新密码",class:"input"},null,8,["modelValue"])])),_:1}),c(V,{class:"password-tips"},{default:r((()=>[c(_,{class:"tip-title"},{default:r((()=>[f("密码要求：")])),_:1}),c(_,{class:"tip-item"},{default:r((()=>[f("• 长度不少于6位")])),_:1}),c(_,{class:"tip-item"},{default:r((()=>[f("• 必须包含字母和数字")])),_:1}),c(_,{class:"tip-item"},{default:r((()=>[f("• 建议包含特殊字符")])),_:1})])),_:1})])),_:1}),c(V,{class:"button-section"},{default:r((()=>[c(b,{class:"submit-btn",onClick:p.changePassword,disabled:t.loading},{default:r((()=>[f(m(t.loading?"修改中...":"确认修改"),1)])),_:1},8,["onClick","disabled"])])),_:1})])),_:1})])),_:1})}],["__scopeId","data-v-001c8e76"]]);export{_ as default};

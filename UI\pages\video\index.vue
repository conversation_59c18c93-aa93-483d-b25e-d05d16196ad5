<template>
  <view class="container">

    <!-- 视频播放器 -->
    <view v-if="!isFullscreen && !showAuditModal" class="video-content">
      <video id="mainVideo" :src="currentVideo.url" :poster="currentVideo.cover" class="video-player" controls
        :enable-progress-gesture="false" :show-progress="true" :page-gesture="false" @ended="onVideoEnded"
        @timeupdate="onTimeUpdate" @seeking="onSeeking" @seeked="onSeeked" @play="onPlay" @pause="onPause"
        @loadedmetadata="onLoadedMetadata"></video>

      <!-- 视频信息 -->
      <view class="video-info">
        <text class="video-title">{{ currentVideo.title }}</text>
        <text class="video-desc">{{ currentVideo.description }}</text>
      </view>
    </view>

    <!-- 问答组件 - 始终显示，但根据视频完成状态控制交互 -->
    <view v-if="!isFullscreen && !showAuditModal && quizData.questions && quizData.questions.length > 0"
      class="quiz-section">
      <VideoQuiz :questions="quizData.questions" :rewardAmount="currentVideo.rewardAmount || 0"
        :videoCompleted="videoCompleted" :hasAnswered="hasAnswered" :answerResult="answerResult" @submit="onQuizSubmit"
        @complete="onQuizComplete" />
    </view>

    <!-- 审核状态弹窗 -->
    <view v-if="showAuditModal" class="audit-modal">
      <view class="audit-modal-content">
        <view class="audit-header">
          <text class="audit-icon">⚠️</text>
          <text class="audit-title">账号待审核</text>
        </view>

        <view class="audit-user-info" v-if="auditUserInfo">
          <view class="user-avatar">
            <image :src="auditUserInfo.avatar" class="avatar-img" mode="aspectFill"></image>
          </view>
          <view class="user-details">
            <text class="user-name">{{ auditUserInfo.nickname }}</text>
            <text class="user-id">用户ID: {{ auditUserInfo.id }}</text>
          </view>
        </view>

        <view class="audit-message">
          <text class="message-text">您的账号正在审核中，暂时无法使用视频功能。</text>
          <text class="contact-text">请联系管理员进行账号审核。</text>
        </view>

        <view class="audit-actions">
          <button class="contact-btn" @click="contactAdmin">联系管理员</button>
        </view>
      </view>
    </view>

    <!-- 开发测试工具 -->
    <!-- #ifdef H5 -->
    <view class="dev-tools" v-if="showDevTools">
      <button class="dev-btn clear-btn" @click="clearCache">清除缓存</button>
      <button class="dev-btn hide-btn" @click="showDevTools = false">隐藏</button>
    </view>
    <view class="dev-toggle" @click="showDevTools = !showDevTools" v-if="!showDevTools">
      <text class="dev-icon">🔧</text>
    </view>
    <!-- #endif -->
  </view>
</template>

<script>
import VideoQuiz from "../../components/VideoQuiz.vue";
import wechatUserService from "@/utils/wechatUserService.js";
import wechatApi from "@/utils/wechatRequest.js";
import { getBatchDetail } from "@/api/batch.js";
import mediaCommonMixin from "@/mixins/media-common.js";

export default {
  name: "VideoIndex",
  mixins: [mediaCommonMixin],
  components: {
    VideoQuiz
  },

  data () {
    return {
      // 视频相关
      currentVideo: {
        url: '',
        cover: '',
        title: '加载中...',
        description: '',
        rewardAmount: 0,
        duration: 0
      },
      videoCompleted: false,
      isFullscreen: false,
      isPlaying: false,
      maxWatchTime: 0,
      currentPlayTime: 0,

      // 问答相关
      quizData: { questions: [] },
      hasAnswered: false,
      answerResult: null,

      // 审核状态
      showAuditModal: false,
      auditUserInfo: null,

      // 页面参数
      batchId: null,
      sharerId: null,
      authCompleted: false,

      // 观看记录相关
      viewRecordId: null,
      lastProgressUpdate: 0,

      // 防重复标志
      isAuthenticating: false,

      // 开发工具
      showDevTools: false
    };
  },

  async onLoad (options) {
    try {
      // 获取页面参数
      this.batchId = options.batchId ? parseInt(options.batchId) : null;
      this.sharerId = options.sharerId;

      // 检查授权完成状态（从缓存中读取，而不是URL参数）
      const authStatus = uni.getStorageSync('wechat_auth_status');
      this.authCompleted = authStatus && authStatus.completed === true;

      // 如果刚完成授权，清理授权状态缓存并处理未审核用户信息
      if (this.authCompleted) {
        uni.removeStorageSync('wechat_auth_status');

        const tempUserData = uni.getStorageSync('tempUnauditedUser');
        if (tempUserData) {
          this.auditUserInfo = {
            nickname: tempUserData.userInfo.nickname || '',
            avatar: tempUserData.userInfo.avatar || '',
            auditStatus: tempUserData.auditStatus || 0
          };
          uni.removeStorageSync('tempUnauditedUser');
        }
      }

      // 清理过期的授权会话缓存（防止缓存积累）
      this.cleanExpiredAuthSessions();

      // 检查是否是微信回调，如果是则重定向到专门的回调页面处理
      if (options.code && options.state) {
        console.log('检测到微信回调参数，重定向到回调页面处理');

        // 缓存当前页面参数，供回调页面使用
        const currentPageParams = {
          batchId: this.batchId,
          sharerId: this.sharerId,
          returnUrl: '/pages/video/index'
        };
        uni.setStorageSync('wechat_page_params', currentPageParams);

        // 重定向到回调页面
        uni.redirectTo({
          url: `/pages/wechat-callback/index?code=${options.code}&state=${options.state}`
        });
        return;
      }

      // 正常的用户认证流程
      await this.authenticateUser();

    } catch (error) {
      console.error('页面初始化失败:', error.message);
      uni.showToast({
        title: '页面加载失败',
        icon: 'error'
      });
    }
  },

  methods: {


    /**
     * 用户认证
     */
    async authenticateUser () {
      // 防止重复认证
      if (this.isAuthenticating) {
        return;
      }

      try {
        this.isAuthenticating = true;

        // 检查用户是否已经登录
        if (wechatUserService.isLoggedIn()) {
          await this.loadVideoData();
          return;
        }

        // 如果刚完成授权但没有缓存，说明用户未审核通过
        if (this.authCompleted) {
          this.showAuditModal = true;
          return;
        }

        // 在H5环境下，显示提示而不是直接跳转
        // #ifdef H5
        uni.showModal({
          title: '需要微信授权',
          content: '请点击确定进行微信授权登录',
          success: async (res) => {
            if (res.confirm) {
              // 在授权前，缓存当前页面的所有参数
              const currentPageParams = {
                batchId: this.batchId,
                sharerId: this.sharerId,
                returnUrl: '/pages/video/index'
              };
              uni.setStorageSync('wechat_page_params', currentPageParams);

              const loginOptions = {
                employeeId: null,
                batchId: this.batchId,
                sharerId: this.sharerId,
                returnUrl: '/pages/video/index'
              };

              try {
                await wechatUserService.tryWechatAutoLogin(loginOptions);
              } catch (error) {
                console.error('微信授权失败:', error);
                uni.showModal({
                  title: '授权失败',
                  content: `微信授权失败：${error.message || '未知错误'}`,
                  showCancel: false,
                  confirmText: '确定'
                });
              }
            } else {
              uni.showToast({
                title: '需要授权才能使用',
                icon: 'none'
              });
            }
          }
        });
        return;
        // #endif

        // 小程序环境下的处理
        // #ifdef MP-WEIXIN
        uni.showLoading({ title: '微信授权中...', mask: true });

        const loginOptions = {
          employeeId: null,
          batchId: this.batchId,
          sharerId: this.sharerId,
          returnUrl: '/pages/video/index'
        };

        await wechatUserService.tryWechatAutoLogin(loginOptions);
        uni.hideLoading();
        await this.loadVideoData();
        // #endif

      } catch (error) {
        uni.hideLoading();
        console.error('用户认证失败:', error);

        // 如果是审核状态问题，显示审核弹窗
        if (error.message && error.message.includes('审核状态')) {
          this.showAuditModal = true;
          return;
        }

        // 其他错误，显示错误提示但不显示审核弹窗
        uni.showToast({
          title: '登录失败，请重试',
          icon: 'error'
        });
      } finally {
        this.isAuthenticating = false;
      }
    },

    /**
     * 显示审核状态弹窗
     */
    showAuditStatusModal () {
      const userInfo = wechatUserService.getUserInfo();

      this.auditUserInfo = userInfo || {
        id: 'unknown',
        nickname: '微信用户',
        avatar: '/static/images/default-avatar.png'
      };

      this.showAuditModal = true;
    },

    /**
     * 联系管理员
     */
    contactAdmin () {
      uni.showModal({
        title: '联系管理员',
        content: '请通过微信群或电话联系管理员，管理员会尽快为您审核账号。',
        showCancel: false,
        confirmText: '知道了'
      });
    },

    /**
     * 加载视频数据
     */
    async loadVideoData () {
      try {
        uni.showLoading({ title: '加载视频中...', mask: true });

        if (!this.batchId) {
          throw new Error('缺少批次ID');
        }

        // 从批次获取数据（包含视频信息和题目）
        await this.loadDataFromBatch();

        // 开始记录观看
        await this.startWatchRecord();

        // 加载用户观看状态
        await this.loadUserWatchStatus();

        // 加载用户答题状态
        await this.loadUserAnswerStatus();

        uni.hideLoading();
      } catch (error) {
        uni.hideLoading();
        console.error('加载视频数据失败:', error);
        uni.showToast({
          title: error.message || '加载失败',
          icon: 'error'
        });
      }
    },

    /**
     * 从批次获取数据
     */
    async loadDataFromBatch () {
      const response = await getBatchDetail(this.batchId);

      if (!response.success || !response.data) {
        throw new Error(response.msg || '获取批次详情失败');
      }

      const batch = response.data;

      // 从批次数据中提取视频信息
      const videoUrl = this.buildCompleteFileUrl(batch.videoUrl);
      const coverUrl = this.buildCompleteFileUrl(batch.videoCoverUrl);

      this.currentVideo = {
        id: batch.videoId,
        title: batch.videoTitle || '视频标题',
        cover: coverUrl || '/static/images/default-cover.jpg',
        url: videoUrl || '',
        duration: batch.videoDuration || 0,
        description: batch.videoDescription || '',
        rewardAmount: batch.rewardAmount || 0
      };

      // 处理题目数据
      this.processQuizData(batch.questions || []);
    },

    /**
     * 开始记录观看
     */
    async startWatchRecord () {
      try {
        // 获取当前用户ID
        const userInfo = wechatUserService.getUserInfo();
        if (!userInfo || !userInfo.id) {
          return;
        }

        // 调用API创建或获取观看记录
        const response = await wechatApi.post('/UserBatchRecord/create-or-get', {
          batchId: this.batchId,
          userId: userInfo.id,
          promotionLink: this.sharerId ? `shared_by_${this.sharerId}` : null
        });

        if (response.success && response.data) {
          this.viewRecordId = response.data.id;
        } else {
          console.error('创建观看记录失败:', response.message);
        }
      } catch (error) {
        console.error('开始记录观看失败:', error);
      }
    },

    /**
     * 处理题目数据
     */
    processQuizData (questions) {
      if (!questions || questions.length === 0) {
        this.quizData = { questions: [] };
        return;
      }

      this.quizData = {
        questions: questions.map((q) => {
          // 处理选项数据，可能是字符串需要解析
          let options = [];
          if (q.options) {
            if (typeof q.options === 'string') {
              try {
                options = JSON.parse(q.options);
              } catch (e) {
                console.error('解析选项JSON失败:', e, q.options);
                options = [];
              }
            } else if (Array.isArray(q.options)) {
              options = q.options;
            }
          }

          // 为选项添加ID（A, B, C, D等）
          const processedOptions = options.map((option, optIndex) => ({
            ...option,
            id: String.fromCharCode(65 + optIndex),
            text: option.optionText || option.text || '',
            optionText: option.optionText || option.text || ''
          }));

          // 找到正确答案的ID
          let correctAnswerId = q.correctAnswer;
          if (!correctAnswerId) {
            const correctOptionIndex = options.findIndex(opt => opt.isCorrect);
            if (correctOptionIndex >= 0) {
              correctAnswerId = String.fromCharCode(65 + correctOptionIndex);
            }
          }

          return {
            id: q.id,
            question: q.questionText || q.question || q.title,
            options: processedOptions,
            correctAnswer: correctAnswerId,
            type: q.type || 'single'
          };
        })
      };
    },

    /**
     * 加载用户观看状态
     */
    async loadUserWatchStatus () {
      try {
        // 获取当前用户ID
        const userInfo = wechatUserService.getUserInfo();
        if (!userInfo || !userInfo.id) {
          return;
        }

        // 调用API获取观看状态
        const response = await wechatApi.get(`/UserBatchRecord/${userInfo.id}/${this.batchId}/watch-status`);

        if (response.success && response.data) {
          // 设置观看记录ID
          if (response.data.id) {
            this.viewRecordId = response.data.id;
          }

          // 如果用户已经观看完成，设置视频完成状态
          if (response.data.isCompleted) {
            this.videoCompleted = true;
          }
        }
      } catch (error) {
        console.error('加载用户观看状态失败:', error);
        // 不影响主流程，只记录错误
      }
    },

    /**
     * 加载用户答题状态
     */
    async loadUserAnswerStatus () {
      try {
        // 获取当前用户ID
        const userInfo = wechatUserService.getUserInfo();
        if (!userInfo || !userInfo.id) {
          return;
        }

        // 调用API获取答题状态
        const response = await wechatApi.get(`/UserBatchRecord/${userInfo.id}/${this.batchId}/answer-status`);

        if (response.success && response.data) {
          // 设置答题状态
          this.hasAnswered = response.data.hasAnswered || false;

          // 如果用户已经答题，保存答题结果
          if (response.data.hasAnswered) {
            this.answerResult = {
              totalQuestions: response.data.totalQuestions,
              correctAnswers: response.data.correctAnswers,
              correctRate: response.data.correctRate,
              rewardAmount: response.data.rewardAmount,
              rewardStatus: response.data.rewardStatus,
              rewardStatusText: response.data.rewardStatusText,
              answerTime: response.data.answerTime
            };

            // 如果有答题详情，可以解析并显示
            if (response.data.answerDetails) {
              try {
                const answerDetails = JSON.parse(response.data.answerDetails);
                this.answerResult.answerDetails = answerDetails;
              } catch (e) {
                console.error('解析答题详情失败:', e);
              }
            }
          }
        } else {
          // 默认未答题
          this.hasAnswered = false;
        }
      } catch (error) {
        console.error('加载用户答题状态失败:', error);
        // 默认未答题，不影响主流程
        this.hasAnswered = false;
      }
    },

    /**
     * 视频播放结束
     */
    onVideoEnded () {
      this.videoCompleted = true;
      this.isPlaying = false;

      // 视频播放完成，设置最大观看时间为视频总时长
      this.maxWatchTime = this.currentVideo.duration || Number.MAX_VALUE;

      // 显示题目
      this.showQuizAfterVideo();

      // 提示用户可以答题
      uni.showToast({
        title: "视频播放完成，可以答题了",
        icon: "success",
        duration: 2000
      });
    },

    /**
     * 视频结束后的处理
     */
    showQuizAfterVideo () {
      // 题目已经始终显示，这里只需要处理视频完成的逻辑
      if (this.quizData.questions && this.quizData.questions.length > 0) {
        // 题目已经显示，用户现在可以进行答题
      } else {
        this.onQuizComplete({ success: true, answers: [] });
      }
    },

    /**
     * 视频时间更新
     */
    onTimeUpdate (e) {
      const currentTime = e.detail.currentTime;
      const duration = e.detail.duration || this.currentVideo.duration || 0;

      // 防快进检查：如果当前时间超过最大观看时间+2秒，则回退
      if (currentTime > this.maxWatchTime + 2) {
        const videoContext = uni.createVideoContext('mainVideo', this);
        if (videoContext) {
          videoContext.seek(this.maxWatchTime);
        }
        uni.showToast({
          title: "请完整观看视频，不允许快进",
          icon: "none",
          duration: 2000
        });
        return;
      }

      // 更新最大观看时间
      if (currentTime > this.maxWatchTime) {
        this.maxWatchTime = currentTime;
      }

      // 更新当前播放时间和时长
      this.currentPlayTime = currentTime;
      if (duration > 0) {
        this.currentVideo.duration = duration;
      }

      // 每10秒更新一次观看进度到服务器
      if (currentTime - this.lastProgressUpdate >= 10) {
        this.updateWatchProgress(currentTime);
        this.lastProgressUpdate = currentTime;
      }
    },

    /**
     * 监听用户开始拖拽进度条
     */
    onSeeking (e) {
      const seekTime = e.detail.currentTime || 0;

      // 如果用户试图拖拽到超过最大观看时间的位置，阻止并回退
      if (seekTime > this.maxWatchTime + 2) {
        setTimeout(() => {
          const videoContext = uni.createVideoContext('mainVideo', this);
          if (videoContext) {
            videoContext.seek(this.maxWatchTime);
          }
        }, 100);

        uni.showToast({
          title: "不允许快进，请完整观看",
          icon: "none",
          duration: 2000
        });
      }
    },

    /**
     * 监听用户完成拖拽进度条
     */
    onSeeked (e) {
      const seekTime = e.detail.currentTime || 0;

      // 再次检查，确保用户没有快进
      if (seekTime > this.maxWatchTime + 2) {
        const videoContext = uni.createVideoContext('mainVideo', this);
        if (videoContext) {
          videoContext.seek(this.maxWatchTime);
        }

        uni.showToast({
          title: "已回退到正确位置",
          icon: "none",
          duration: 1500
        });
      }
    },

    /**
     * 视频开始播放
     */
    onPlay () {
      this.isPlaying = true;
    },

    /**
     * 视频暂停
     */
    onPause () {
      this.isPlaying = false;
    },

    /**
     * 视频元数据加载完成
     */
    onLoadedMetadata (e) {
      if (e.detail.duration) {
        this.currentVideo.duration = e.detail.duration;
      }
    },

    /**
     * 更新观看进度
     */
    async updateWatchProgress (currentTime) {
      try {
        // 如果没有观看记录ID，先尝试创建记录
        if (!this.viewRecordId) {
          await this.startWatchRecord();
          if (!this.viewRecordId) {
            return;
          }
        }

        // 获取当前用户ID
        const userInfo = wechatUserService.getUserInfo();
        if (!userInfo || !userInfo.id) {
          return;
        }

        // 计算观看进度百分比
        const watchProgress = this.currentVideo.duration > 0 ? currentTime / this.currentVideo.duration : 0;
        const isCompleted = watchProgress >= 0.95;

        // 调用API更新观看进度
        const response = await wechatApi.post(`/UserBatchRecord/${userInfo.id}/watch-progress`, {
          batchId: this.batchId,
          viewDuration: Math.floor(currentTime),
          watchProgress: Math.min(watchProgress, 1.0),
          isCompleted: isCompleted
        });

        if (!response.success) {
          console.error('更新观看进度失败:', response.message);
        }
      } catch (error) {
        console.error('更新观看进度失败:', error);
      }
    },

    /**
     * 问答提交
     */
    async onQuizSubmit (answers) {
      try {
        // 获取当前用户ID
        const userInfo = wechatUserService.getUserInfo();
        if (!userInfo || !userInfo.id) {
          console.error('用户信息不完整，无法提交答题');
          return;
        }

        // 准备答题数据
        const submitData = {
          batchId: this.batchId,
          totalQuestions: answers.totalQuestions,
          correctAnswers: answers.correctCount,
          answerDetails: JSON.stringify(answers.answerDetails)
        };

        // 调用API提交答题
        const response = await wechatApi.post(`/UserBatchRecord/${userInfo.id}/submit-answer`, submitData);

        if (!response.success) {
          console.error('答题提交失败:', response.message);
          uni.showToast({
            title: '答题提交失败',
            icon: 'error'
          });
        }
      } catch (error) {
        console.error('答题提交异常:', error);
        uni.showToast({
          title: '答题提交失败',
          icon: 'error'
        });
      }
    },

    /**
     * 问答完成
     */
    onQuizComplete () {
      // 答题完成后发放红包
      this.claimReward();
    },

    /**
     * 领取红包奖励
     */
    async claimReward () {
      try {
        // 这里调用API领取红包
        // const response = await wechatApi.post('/UserBatchRecord/claim-reward', {
        //   batchId: this.batchId
        // });

        // 记录分享人奖励
        if (this.sharerId) {
          this.recordSharerReward();
        }
      } catch (error) {
        console.error('领取红包失败:', error);
      }
    },

    /**
     * 记录分享人奖励
     */
    async recordSharerReward () {
      try {
        // 这里调用API记录分享人奖励
        // const response = await wechatApi.post('/UserBatchRecord/sharer-reward', {
        //   batchId: this.batchId,
        //   sharerId: this.sharerId
        // });
      } catch (error) {
        console.error('记录分享人奖励失败:', error);
      }
    },

    /**
     * 清理URL中的授权参数并重新加载页面
     */
    cleanUrlAndReload () {
      try {
        // 设置授权完成状态到缓存
        uni.setStorageSync('wechat_auth_status', {
          completed: true,
          timestamp: Date.now()
        });

        // 构造纯净的URL，只保留业务参数
        const params = [];
        if (this.batchId) {
          params.push(`batchId=${this.batchId}`);
        }
        if (this.sharerId) {
          params.push(`sharerId=${this.sharerId}`);
        }

        const cleanUrl = '/pages/video/index' + (params.length > 0 ? '?' + params.join('&') : '');

        console.log('清理授权参数，跳转到纯净URL:', cleanUrl);

        // 使用 reLaunch 跳转到纯净URL
        uni.reLaunch({
          url: cleanUrl
        });
      } catch (error) {
        console.error('清理URL失败:', error);
      }
    },

    /**
     * 清理过期的授权会话缓存
     */
    cleanExpiredAuthSessions () {
      try {
        const storageInfo = uni.getStorageInfoSync();
        const now = Date.now();
        const expireTime = 30 * 60 * 1000; // 30分钟过期

        storageInfo.keys.forEach(key => {
          if (key.startsWith('wechat_auth_session_')) {
            try {
              const sessionData = uni.getStorageSync(key);
              if (sessionData && sessionData.timestamp) {
                if (now - sessionData.timestamp > expireTime) {
                  uni.removeStorageSync(key);
                  console.log('清理过期授权会话:', key);
                }
              }
            } catch (error) {
              // 如果读取失败，直接删除
              uni.removeStorageSync(key);
            }
          }
        });
      } catch (error) {
        console.error('清理过期授权会话失败:', error);
      }
    },

    /**
     * 清除缓存（开发测试用）
     */
    clearCache () {
      uni.showModal({
        title: '清除缓存',
        content: '确定要清除所有用户缓存数据吗？清除后需要重新登录。',
        success: (res) => {
          if (res.confirm) {
            try {
              // 清除用户相关的缓存
              wechatUserService.clearUserInfo();

              // 清除其他可能的缓存
              uni.clearStorageSync();

              uni.showToast({
                title: '缓存已清除',
                icon: 'success'
              });

              // 延迟刷新页面
              setTimeout(() => {
                uni.reLaunch({
                  url: `/pages/video/index?batchId=${this.batchId}${this.sharerId ? `&sharerId=${this.sharerId}` : ''}`
                });
              }, 1500);
            } catch (error) {
              console.error('清除缓存失败:', error);
              uni.showToast({
                title: '清除失败',
                icon: 'error'
              });
            }
          }
        }
      });
    }

  },

  // 页面销毁时清理定时器
  onUnload () {
    if (this.progressUpdateTimer) {
      clearInterval(this.progressUpdateTimer);
      this.progressUpdateTimer = null;
    }
  }
};
</script>

<style>
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.video-content {
  flex: 1;
  padding: 20rpx;
}

.video-player {
  width: 100%;
  height: 400rpx;
  border-radius: 12rpx;
}

.video-info {
  margin-top: 20rpx;
  padding: 20rpx;
  background: white;
  border-radius: 12rpx;
}

.video-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.video-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.quiz-section {
  padding: 20rpx;
}

/* 审核弹窗样式 */
.audit-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
}

.audit-modal-content {
  width: 600rpx;
  background: white;
  border-radius: 20rpx;
  padding: 40rpx;
}

.audit-header {
  text-align: center;
  margin-bottom: 30rpx;
}

.audit-icon {
  font-size: 80rpx;
  display: block;
  margin-bottom: 20rpx;
}

.audit-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.audit-user-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
}

.user-avatar {
  margin-right: 20rpx;
}

.avatar-img {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.user-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 5rpx;
}

.user-id {
  font-size: 24rpx;
  color: #666;
}

.audit-message {
  text-align: center;
  margin-bottom: 30rpx;
}

.message-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  display: block;
  margin-bottom: 10rpx;
}

.contact-text {
  font-size: 26rpx;
  color: #999;
}

.contact-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  text-align: center;
}

/* 悬浮刷新审核状态按钮 */
.floating-refresh-btn {
  position: fixed !important;
  top: 50rpx;
  left: 30rpx;
  width: 100rpx;
  height: 100rpx;
  background: #1890ff;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
  z-index: 9999 !important;
  cursor: pointer;
  pointer-events: auto !important;
}

.floating-refresh-btn:hover {
  background: #40a9ff;
  transform: scale(1.05);
}

.refresh-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}

.refresh-text {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
}

/* 悬浮重新登录按钮 */
.floating-relogin-btn {
  position: fixed !important;
  top: 170rpx;
  left: 30rpx;
  width: 100rpx;
  height: 100rpx;
  background: #52c41a;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(82, 196, 26, 0.3);
  z-index: 9999 !important;
  cursor: pointer;
  pointer-events: auto !important;
}

.floating-relogin-btn:hover {
  background: #73d13d;
  transform: scale(1.05);
}

.relogin-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}

.relogin-text {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
}

/* 悬浮调试按钮 */
.floating-debug-btn {
  position: fixed !important;
  top: 290rpx;
  left: 30rpx;
  width: 100rpx;
  height: 100rpx;
  background: #722ed1;
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(114, 46, 209, 0.3);
  z-index: 9999 !important;
  cursor: pointer;
  pointer-events: auto !important;
}

.floating-debug-btn:hover {
  background: #9254de;
  transform: scale(1.05);
}

.debug-icon {
  font-size: 32rpx;
  margin-bottom: 4rpx;
}

.debug-text {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
}

/* 开发工具样式 */
.dev-tools {
  position: fixed;
  top: 20rpx;
  right: 20rpx;
  display: flex;
  gap: 10rpx;
  z-index: 9999;
}

.dev-btn {
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  border: none;
  color: white;
  font-size: 24rpx;
  cursor: pointer;
}

.dev-btn.clear-btn {
  background: #ff4d4f;
}

.dev-btn.clear-btn:hover {
  background: #ff7875;
}

.dev-btn.hide-btn {
  background: #666;
}

.dev-btn.hide-btn:hover {
  background: #999;
}

.dev-toggle {
  position: fixed;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  cursor: pointer;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.3);
}

.dev-toggle:hover {
  background: #40a9ff;
  transform: scale(1.05);
}

.dev-icon {
  font-size: 32rpx;
}
</style>

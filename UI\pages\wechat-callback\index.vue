<template>
  <view class="callback-container">
    <view class="callback-content">
      <!-- 加载状态 -->
      <view v-if="isLoading" class="loading-section">
        <u-loading-icon mode="spinner" size="40" color="#186BFF" />
        <text class="loading-text">{{ loadingText }}</text>
      </view>

      <!-- 错误状态 -->
      <view v-else-if="hasError" class="error-section">
        <u-icon name="close-circle" size="60" color="#F5222D" />
        <text class="error-title">授权失败</text>
        <text class="error-message">{{ errorMessage }}</text>
        <u-button type="primary" @click="handleRetry" class="retry-btn">
          重试
        </u-button>
        <u-button type="info" @click="goBack" class="back-btn">
          返回
        </u-button>
      </view>

      <!-- 成功状态 -->
      <view v-else-if="isSuccess" class="success-section">
        <u-icon name="checkmark-circle" size="60" color="#52C41A" />
        <text class="success-title">授权成功</text>
        <text class="success-message">正在跳转...</text>
      </view>
    </view>

    <!-- Toast -->
    <u-toast ref="uToast" />
  </view>
</template>

<script>
import wechatWebAuth from '@/utils/wechatWebAuth.js'
import { wechatWebLogin } from '@/utils/wechatUserService.js'

export default {
  data () {
    return {
      isLoading: true,
      hasError: false,
      isSuccess: false,
      errorMessage: '',
      loadingText: '正在处理授权信息...',
      callbackResult: null,
      retryCount: 0,
      maxRetries: 3
    }
  },

  onLoad (options) {
    console.log('微信授权回调页面加载，参数:', options)
    this.handleWechatCallback()
  },

  methods: {
    /**
     * 处理微信授权回调
     */
    async handleWechatCallback () {
      try {
        this.isLoading = true
        this.hasError = false
        this.loadingText = '正在处理授权信息...'

        // 解析回调参数
        const callbackResult = wechatWebAuth.handleCallback()
        this.callbackResult = callbackResult

        console.log('微信授权回调结果:', callbackResult)

        if (!callbackResult.success) {
          this.showError(callbackResult.message || '授权失败')
          return
        }

        // 检查是否有授权码
        if (!callbackResult.code) {
          this.showError('未获取到授权码')
          return
        }

        // 调用登录接口
        await this.performLogin(callbackResult)

      } catch (error) {
        console.error('处理微信授权回调失败:', error)
        this.showError('处理授权信息失败: ' + error.message)
      }
    },

    /**
     * 执行登录
     */
    async performLogin (callbackResult) {
      try {
        this.loadingText = '正在登录...'

        // 准备回调数据
        const callbackData = {
          Code: callbackResult.code,
          State: callbackResult.state
        }

        // 获取 InviterId，优先级：缓存会话数据 > URL参数 > 状态数据
        console.log('InviterId获取调试:', {
          authSessionData: callbackResult.authSessionData,
          urlInviterId: callbackResult.inviterId,
          stateDataSharerID: callbackResult.stateData?.sharerId,
          stateDataInviterId: callbackResult.stateData?.InviterId
        })

        if (callbackResult.authSessionData && callbackResult.authSessionData.inviterId) {
          // 最高优先级：使用缓存的授权会话数据中的InviterId
          callbackData.InviterId = callbackResult.authSessionData.inviterId
          console.log('使用缓存会话数据中的InviterId:', callbackResult.authSessionData.inviterId)
        } else if (callbackResult.inviterId) {
          // 次优先级：使用URL参数中的InviterId
          callbackData.InviterId = callbackResult.inviterId
          console.log('使用URL参数中的InviterId:', callbackResult.inviterId)
        } else if (callbackResult.stateData) {
          // 最低优先级：从状态数据中提取 InviterId，兼容新旧版本
          if (callbackResult.stateData.sharerId) {
            // 新版本：从 sharerId 字段获取
            callbackData.InviterId = callbackResult.stateData.sharerId
            console.log('使用状态数据中的sharerId:', callbackResult.stateData.sharerId)
          } else if (callbackResult.stateData.InviterId) {
            // 旧版本兼容：从 InviterId 字段获取
            callbackData.InviterId = callbackResult.stateData.InviterId
            console.log('使用状态数据中的InviterId:', callbackResult.stateData.InviterId)
          }
        }

        console.log('最终的InviterId:', callbackData.InviterId)

        console.log('准备调用登录接口，数据:', callbackData)

        // 调用H5端微信登录接口
        const response = await wechatWebLogin(callbackData)

        console.log('登录接口响应:', response)

        if (response.success && response.data) {
          // 登录成功，保存用户信息
          await this.handleLoginSuccess(response.data)
        } else {
          throw new Error(response.message || '登录失败')
        }

      } catch (error) {
        console.error('登录失败:', error)
        this.showError('登录失败: ' + error.message)
      }
    },

    /**
     * 处理登录成功
     */
    async handleLoginSuccess (loginData) {
      try {
        this.loadingText = '授权成功，正在跳转...'

        // 只有审核通过的用户才缓存信息
        if (loginData.auditStatus === 1) {
          console.log('用户已审核通过，缓存用户信息')
          if (loginData.userInfo) {
            uni.setStorageSync('wechatUserInfo', loginData.userInfo)
          }
          if (loginData.token) {
            uni.setStorageSync('wechatUserToken', loginData.token)
          }
        } else {
          console.log('用户未审核通过，不缓存用户信息，状态:', loginData.auditStatus)
          // 清除可能存在的旧缓存
          uni.removeStorageSync('wechatUserInfo')
          uni.removeStorageSync('wechatUserToken')
        }

        // 显示成功状态
        this.isLoading = false
        this.isSuccess = true

        this.showToastMessage('授权成功！', 'success')

        // 延迟跳转
        setTimeout(() => {
          this.redirectAfterLogin(loginData)
        }, 1500)

      } catch (error) {
        console.error('处理登录成功失败:', error)
        this.showError('保存登录信息失败: ' + error.message)
      }
    },

    /**
     * 登录后跳转
     */
    redirectAfterLogin (loginData) {
      try {
        // 从缓存中获取原始页面参数
        const cachedPageParams = uni.getStorageSync('wechat_page_params') || {}
        console.log('缓存的页面参数:', cachedPageParams)

        // 设置授权完成状态到缓存中，而不是URL参数
        uni.setStorageSync('wechat_auth_status', {
          completed: true,
          timestamp: Date.now()
        })

        // 构造纯净的跳转URL，不添加任何额外参数
        let redirectUrl = cachedPageParams.returnUrl || '/pages/video/index'
        const params = []

        // 只添加原始的业务参数
        if (cachedPageParams.batchId) {
          params.push(`batchId=${cachedPageParams.batchId}`)
        }

        if (cachedPageParams.sharerId) {
          params.push(`sharerId=${cachedPageParams.sharerId}`)
        }

        // 如果用户未审核通过，临时存储用户信息（获取后立即删除）
        if (loginData.auditStatus !== 1 && loginData.userInfo) {
          const tempUserData = {
            userInfo: loginData.userInfo,
            auditStatus: loginData.auditStatus
          }
          uni.setStorageSync('tempUnauditedUser', tempUserData)
          console.log('临时存储未审核用户信息（一次性使用）:', tempUserData)
        }

        if (params.length > 0) {
          redirectUrl += '?' + params.join('&')
        }

        console.log('登录成功，跳转到纯净URL:', redirectUrl)

        // 清理页面参数缓存（已经使用完毕）
        uni.removeStorageSync('wechat_page_params')

        // 跳转到视频页面
        uni.reLaunch({
          url: redirectUrl,
          fail: (error) => {
            console.error('跳转失败:', error)
            // 如果跳转失败，尝试跳转到首页
            uni.reLaunch({
              url: '/pages/index/index'
            })
          }
        })

      } catch (error) {
        console.error('跳转失败:', error)
        this.showError('跳转失败: ' + error.message)
      }
    },

    /**
     * 显示错误
     */
    showError (message) {
      this.isLoading = false
      this.hasError = true
      this.errorMessage = message
      this.showToastMessage(message, 'error')
    },

    /**
     * 重试处理
     */
    async handleRetry () {
      if (this.retryCount >= this.maxRetries) {
        this.showToastMessage('重试次数过多，请稍后再试', 'warning')
        return
      }

      this.retryCount++
      console.log(`第 ${this.retryCount} 次重试`)

      await this.handleWechatCallback()
    },

    /**
     * 返回上一页
     */
    goBack () {
      // 跳转到视频页面
      uni.reLaunch({
        url: '/pages/video/index'
      })
    },

    /**
     * 显示Toast消息
     */
    showToastMessage (message, type = 'success') {
      this.$refs.uToast.show({
        message: message,
        type: type,
        duration: 3000
      })
    }
  }
}
</script>

<style scoped>
.callback-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f2f5 0%, #fafbfc 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx 30rpx;
  box-sizing: border-box;
}

.callback-content {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 80rpx 60rpx;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 600rpx;
  width: 100%;
}

/* 加载状态样式 */
.loading-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.loading-text {
  font-size: 32rpx;
  color: #666666;
  margin-top: 20rpx;
}

/* 错误状态样式 */
.error-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.error-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #F5222D;
  margin-top: 20rpx;
}

.error-message {
  font-size: 28rpx;
  color: #666666;
  text-align: center;
  line-height: 1.5;
}

.retry-btn,
.back-btn {
  margin-top: 20rpx;
  width: 200rpx;
}

/* 成功状态样式 */
.success-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

.success-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #52C41A;
  margin-top: 20rpx;
}

.success-message {
  font-size: 28rpx;
  color: #666666;
}
</style>

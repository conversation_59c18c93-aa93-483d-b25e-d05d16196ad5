function __vite__mapDeps(indexes) {
  if (!__vite__mapDeps.viteFileDeps) {
    __vite__mapDeps.viteFileDeps = ["assets/utils-wechatWebAuth.CHzwRabY.js","assets/index-BcNPWRTq.js","assets/index-BYmBPzi2.css","assets/wechat.BxyZ_GJG.js"]
  }
  return indexes.map((i) => __vite__mapDeps.viteFileDeps[i])
}
import{a7 as e,L as t,J as r,K as s,M as o}from"./index-BcNPWRTq.js";import{w as a}from"./video-user.CxwqIREm.js";import{w as n,f as c}from"./wechat.BxyZ_GJG.js";const i="wechatUserInfo",u="wechatUserToken";const l=new class{async wechatLogin(e){try{const t=await a(e);if(t.success&&t.data){const e=t.data.userToken||t.data.token;if(void 0!==t.data.auditStatus&&1!==t.data.auditStatus){const e=0===t.data.auditStatus?"待审核":"已拒绝";throw new Error(`用户审核状态：${e}，无法使用业务功能`)}return this.saveUserInfo(t.data.userInfo),e&&this.saveUserToken(e),{success:!0,message:"微信登录成功",data:{...t.data,token:e}}}throw new Error(t.message||"微信登录失败")}catch(t){return console.error("微信登录失败:",t),{success:!1,message:t.message||"微信登录失败，请重试"}}}async tryWechatAutoLogin(e){try{return await this.tryWechatWebAuth(e)}catch(t){throw console.error("微信自动登录失败:",t),t}}async tryWechatWebAuth(t){try{const r=(await e((()=>import("./utils-wechatWebAuth.CHzwRabY.js")),__vite__mapDeps([0,1,2,3]))).default;if(!r.isH5Environment())throw new Error("当前环境不支持微信网页授权");const s={batchId:t.batchId?parseInt(t.batchId):null,returnUrl:t.returnUrl||"/pages/index/index"};return await r.startAuth({extraState:s,inviterId:t.sharerId||t.employeeId||null}),{success:!0,message:"正在跳转到微信授权页面..."}}catch(r){throw console.error("微信网页授权失败:",r),r}}async wechatWebLogin(e){try{const{Code:t,State:r,InviterId:s}=e,o=await n({Code:t,State:r,InviterId:s||""});if(o.success&&o.data){const e=c(o.data.userInfo||o.data.user),t=o.data.userToken||o.data.token;return 1===o.data.auditStatus?(console.log("用户已审核通过，缓存用户信息"),this.saveUserInfo(e),t&&this.saveUserToken(t)):(console.log("用户未审核通过，不缓存用户信息，状态:",o.data.auditStatus),this.clearUserInfo(),this.clearUserToken()),{success:!0,message:"微信网页登录成功",data:{userInfo:e,token:t,auditStatus:o.data.auditStatus,isNewUser:o.data.isNewUser}}}throw new Error(o.msg||o.message||"微信网页登录失败")}catch(t){return console.error("微信网页登录失败:",t),{success:!1,message:t.message||"微信网页登录失败，请重试"}}}saveUserInfo(e){try{t(i,e)}catch(r){console.error("Error saving wechat user info:",r)}}saveUserToken(e){try{t(u,e)}catch(r){console.error("Error saving wechat user token:",r)}}getUserInfo(){try{return r(i)||null}catch(e){return console.error("Error getting wechat user info:",e),null}}getUserToken(){try{return r(u)||null}catch(e){return console.error("Error getting wechat user token:",e),null}}isLoggedIn(){const e=this.getUserInfo(),t=this.getUserToken();return!(!e||!t)}getUserId(){const e=this.getUserInfo();return(null==e?void 0:e.id)??null}getNickname(){const e=this.getUserInfo();return(null==e?void 0:e.nickname)??null}getAvatar(){const e=this.getUserInfo();return(null==e?void 0:e.avatar)??null}getOpenId(){const e=this.getUserInfo();return(null==e?void 0:e.openId)??null}getEmployeeId(){const e=this.getUserInfo();return(null==e?void 0:e.employeeId)??null}logout(){try{return s(i),s(u),console.log("微信用户已登出"),!0}catch(e){return console.error("Error during wechat user logout:",e),!1}}getDisplayInfo(){const e=this.getUserInfo();return e?{id:e.id,nickname:e.nickname||"微信用户",avatar:e.avatar||"/static/logo.png",openId:e.openId,employeeId:e.employeeId,userType:"wechat_user",createTime:e.createTime,lastLogin:e.lastLogin}:{nickname:"未登录",avatar:"/static/logo.png",userType:"guest"}}redirectToLogin(e=""){const t=e?`/pages/user-login/index?returnUrl=${encodeURIComponent(e)}`:"/pages/user-login/index";o({url:t})}requireAuth(){return!this.isLoggedIn()&&(console.log("微信用户未登录，需要认证"),!0)}clearUserInfo(){try{s(i),console.log("已清除微信用户信息")}catch(e){console.error("清除用户信息失败:",e)}}clearUserToken(){try{s(u),console.log("已清除微信用户Token")}catch(e){console.error("清除用户Token失败:",e)}}clearAll(){this.clearUserInfo(),this.clearUserToken(),console.log("已清除所有微信用户数据")}},d=e=>l.wechatWebLogin(e);export{d as a,l as w};

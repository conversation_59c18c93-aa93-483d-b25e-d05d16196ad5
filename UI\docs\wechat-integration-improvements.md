# 微信用户集成功能完善

根据后台API文档 (`api.json`) 对微信用户相关功能进行了完善和修正。

## 主要改进

### 1. 修正API接口路径
- **微信OAuth回调接口**：从 `/Wechat/callback` 修正为 `/Wechat/oauth-callback`
- **参数名称统一**：将 `Code/State` 修正为 `code/state`

### 2. Token处理优化
- **双Token支持**：正确处理 `accessToken`（管理系统用）和 `userToken`（用户业务用）
- **优先使用userToken**：业务功能优先使用7天有效期的 `userToken`
- **Token类型标识**：在请求头中正确使用 `Bearer {userToken}`

### 3. 用户审核状态检查
- **审核状态验证**：登录时检查 `auditStatus`，只有状态为1（已通过）的用户才能使用业务功能
- **状态提示**：对待审核（0）和已拒绝（2）用户给出明确提示

### 4. 完善业务流程API调用

#### 观看进度管理
- **统一进度接口**：使用 `/UserBatchRecord/create-or-get` 获取用户观看记录和进度
- **进度数据结构**：正确处理 `viewDuration`、`watchProgress`、`isCompleted` 字段
- **完播状态同步**：根据后端返回的 `isCompleted` 状态同步前端状态

#### 答题功能增强
- **答题资格检查**：添加 `/UserBatchRecord/{userId}/answer-eligibility/{batchId}` 接口调用
- **答题状态查询**：添加 `/UserBatchRecord/{userId}/answer-status/{batchId}` 接口调用
- **答题前验证**：确保用户完播且符合答题条件后才允许答题

#### 红包发放优化
- **参数结构调整**：按照API文档要求传递 `rewardAmount`、`transactionId`、`outTradeNo`
- **发放状态跟踪**：支持红包发放状态的跟踪和处理

### 5. 邀请人参数处理
- **参数映射**：将前端的 `sharerId` 正确映射为后端的 `inviterId`
- **状态参数构造**：在微信授权时正确构造包含 `inviterId` 和 `batchId` 的状态参数

### 6. 错误处理增强
- **业务规则验证**：
  - 必须完播视频后才能答题
  - 必须答题完成后才能发放红包
  - 每个批次只能答题一次
  - 审核状态必须为1才能使用业务功能
- **用户友好提示**：对各种业务限制给出清晰的错误提示

## 完整业务流程

根据API文档实现的完整用户业务流程：

1. **微信OAuth登录** → `GET /api/Wechat/oauth-callback`
2. **审核状态检查** → 验证 `auditStatus === 1`
3. **创建观看记录** → `POST /api/UserBatchRecord/create-or-get`
4. **观看视频并更新进度** → `POST /api/UserBatchRecord/{userId}/watch-progress`
5. **检查答题资格** → `GET /api/UserBatchRecord/{userId}/answer-eligibility/{batchId}`
6. **提交答题结果** → `POST /api/UserBatchRecord/{userId}/submit-answer`
7. **发放红包** → `POST /api/UserBatchRecord/{userId}/grant-reward`

## 技术改进

### Token管理
- 使用专门的 `wechatRequest.js` 处理用户业务请求
- 自动添加 `userToken` 到请求头
- Token过期时自动重新授权

### 数据一致性
- 前后端数据结构完全对齐
- 字段命名和类型严格按照API文档
- 业务规则与后端保持一致

### 用户体验
- 审核状态清晰提示
- 答题资格自动检查
- 业务流程无缝衔接

## 文件修改清单

1. **UI/api/wechat.js** - 修正OAuth回调接口路径和参数
2. **UI/utils/wechatUserService.js** - 完善Token处理和审核状态检查
3. **UI/utils/wechatRequest.js** - 确保正确使用userToken
4. **UI/pages/video/index.vue** - 完善业务流程API调用
5. **UI/api/video-user.js** - 添加答题相关API接口

所有修改都严格按照 `api.json` 文档的接口定义进行，确保前后端完全兼容。

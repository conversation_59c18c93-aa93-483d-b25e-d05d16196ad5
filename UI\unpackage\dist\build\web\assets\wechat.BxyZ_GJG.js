import{m as n,L as t,J as r,K as a}from"./index-BcNPWRTq.js";function e(t){return n.get("/Wechat/authorize",t)}function o(t){return n.get("/Wechat/callback",t)}function c(n){return n?{id:n.id||n.userId||"",nickname:n.nickname||n.nickName||"微信用户",avatar:n.avatar||n.headimgurl||"",openid:n.openid||"",unionid:n.unionid||"",gender:n.gender||0,city:n.city||"",province:n.province||"",country:n.country||"",language:n.language||"zh_CN"}:null}function i(n={}){const r=Math.random().toString(36).substring(2,15)+Date.now().toString(36),a={timestamp:Date.now(),random:Math.random().toString(36).substring(2,11),...n};try{return t(`wechat_state_${r}`,a),r}catch(e){return console.error("构建状态参数失败:",e),""}}function u(n){try{if(!n)return null;const e=r(`wechat_state_${n}`);if(e)return a(`wechat_state_${n}`),e;try{const t=atob(n);return JSON.parse(t)}catch(t){return console.warn("无法解析状态参数，可能是新旧版本不兼容:",t),null}}catch(e){return console.error("解析状态参数失败:",e),null}}export{i as b,c as f,e as g,u as p,o as w};

/**
 * 微信用户专用请求工具
 * 专门处理微信用户的API请求，包括token管理和过期处理
 */

import { getApiBaseURL, API_CONFIG } from './config-manager.js'
import { showError, showSuccess } from './toast-manager.js'
import wechatUserService from './wechatUserService.js'

/**
 * 显示确认对话框
 * @param {string} content 对话框内容
 * @param {string} title 对话框标题
 * @returns {Promise<boolean>} 用户选择结果
 */
function showConfirm(content, title = '提示') {
  return new Promise((resolve) => {
    uni.showModal({
      title,
      content,
      confirmText: '确认',
      cancelText: '取消',
      success: (res) => {
        resolve(res.confirm)
      },
      fail: () => {
        resolve(false)
      }
    })
  })
}

/**
 * 处理微信用户认证错误
 * @param {string} message 错误消息
 * @param {Object} options 页面参数，用于重新授权
 */
async function handleWechatAuthError(message, options = {}) {
  console.log('微信用户认证失败:', message)
  
  // 清除过期的用户信息
  wechatUserService.logout()
  
  // 尝试重新授权
  try {
    const result = await wechatUserService.tryWechatAutoLogin(options)
    if (result.success) {
      console.log('微信重新授权成功')
      return true
    } else {
      throw new Error(result.message || '重新授权失败')
    }
  } catch (error) {
    console.error('微信重新授权失败:', error)
    showError('登录已过期，请刷新页面重新登录')
    return false
  }
}

/**
 * 处理网络错误
 * @param {Object} error 错误对象
 */
async function handleNetworkError(error) {
  const shouldRetry = await showConfirm(
    '无法连接到服务器，是否重试？',
    '连接错误'
  )

  if (!shouldRetry) {
    showError('已取消重试')
  }
  
  return shouldRetry
}

/**
 * 微信用户请求拦截器 - 处理请求前的配置
 * @param {Object} config 请求配置
 * @returns {Object} 处理后的请求配置
 */
function wechatRequestInterceptor(config) {
  // 设置基础URL
  if (!config.url.startsWith('http')) {
    config.url = getApiBaseURL() + config.url
  }

  // 设置默认请求头
  config.header = {
    ...API_CONFIG.DEFAULT_HEADERS,
    ...config.header
  }

  // 添加微信用户token（使用userToken）
  const userToken = wechatUserService.getUserToken()
  if (userToken) {
    config.header['Authorization'] = `Bearer ${userToken}`
  }

  // 设置超时时间
  config.timeout = config.timeout || API_CONFIG.TIMEOUT

  return config
}

/**
 * 微信用户响应拦截器 - 处理响应数据和错误
 * @param {Object} response uni.request的响应对象
 * @param {Object} originalConfig 原始请求配置
 * @returns {Promise} 处理后的响应数据或错误
 */
async function wechatResponseInterceptor(response, originalConfig) {
  const { data, statusCode } = response

  // HTTP状态码检查
  if (statusCode !== 200) {
    if (statusCode === 401) {
      // 尝试重新授权
      const reAuthSuccess = await handleWechatAuthError('认证失败', wechatApi.getPageOptions())
      if (reAuthSuccess) {
        // 重新授权成功，重试原始请求
        return wechatRequest(originalConfig)
      } else {
        return Promise.reject(new Error('认证失败'))
      }
    } else if (statusCode >= 500) {
      showError('服务器错误')
      return Promise.reject(new Error('服务器错误'))
    } else {
      showError(`请求失败 (${statusCode})`)
      return Promise.reject(new Error(`请求失败 (${statusCode})`))
    }
  }

  // 业务逻辑状态码检查
  if (data && typeof data === 'object') {
    // 新的API格式: {success: boolean, code: number, msg: string, data: any}
    if (typeof data.success === 'boolean') {
      if (data.success) {
        // 成功响应
        return Promise.resolve(data)
      } else {
        // 失败响应
        const errorMsg = data.msg || '请求失败'

        // 处理认证错误
        if (data.code === 401) {
          const reAuthSuccess = await handleWechatAuthError(errorMsg, wechatApi.getPageOptions())
          if (reAuthSuccess) {
            // 重新授权成功，重试原始请求
            return wechatRequest(originalConfig)
          } else {
            return Promise.reject(new Error(errorMsg))
          }
        }

        // 其他业务错误，返回完整响应让调用方处理
        return Promise.resolve(data)
      }
    }

    // 兼容旧的API格式
    const { code, msg, message } = data

    // 处理成功响应
    if (code === 200 || code === 0) {
      return Promise.resolve(data)
    }

    // 处理业务逻辑错误
    if (code === 500) {
      return Promise.resolve(data)
    }

    // 处理认证错误
    if (code === 401) {
      const reAuthSuccess = await handleWechatAuthError(msg || message || '认证失败', wechatApi.getPageOptions())
      if (reAuthSuccess) {
        return wechatRequest(originalConfig)
      } else {
        return Promise.reject(new Error(msg || message || '认证失败'))
      }
    }

    // 其他错误
    const errorMessage = msg || message || '请求失败'
    return Promise.resolve(data)
  }

  // 直接返回数据
  return Promise.resolve(data)
}

/**
 * 微信用户核心请求方法
 * @param {Object} config 请求配置
 * @returns {Promise} 请求Promise
 */
function wechatRequest(config) {
  return new Promise((resolve, reject) => {
    // 应用请求拦截器
    const processedConfig = wechatRequestInterceptor(config)

    // 发起请求
    uni.request({
      ...processedConfig,
      success: (response) => {
        // 应用响应拦截器
        wechatResponseInterceptor(response, config)
          .then(resolve)
          .catch(reject)
      },
      fail: async (error) => {
        // 处理网络错误
        if (error.errMsg && error.errMsg.includes('timeout')) {
          showError('请求超时，请检查网络连接')
          reject(error)
        } else if (error.errMsg && error.errMsg.includes('fail')) {
          const shouldRetry = await handleNetworkError(error)
          if (shouldRetry) {
            // 重试请求
            wechatRequest(config).then(resolve).catch(reject)
          } else {
            reject(error)
          }
        } else {
          showError('请求配置错误: ' + (error.errMsg || error.message || '未知错误'))
          reject(error)
        }
      }
    })
  })
}

// 便捷方法
const wechatApi = {
  get(url, params = {}, options = {}) {
    return wechatRequest({
      url,
      method: 'GET',
      data: params,
      ...options
    })
  },

  post(url, data = {}, options = {}) {
    return wechatRequest({
      url,
      method: 'POST',
      data,
      ...options
    })
  },

  put(url, data = {}, options = {}) {
    return wechatRequest({
      url,
      method: 'PUT',
      data,
      ...options
    })
  },

  delete(url, params = {}, options = {}) {
    return wechatRequest({
      url,
      method: 'DELETE',
      data: params,
      ...options
    })
  },

  // 设置页面参数，用于重新授权时使用
  setPageOptions(options) {
    this._pageOptions = options
  },

  // 获取页面参数
  getPageOptions() {
    return this._pageOptions || {}
  }
}

export default wechatApi
export { wechatRequest, handleWechatAuthError }

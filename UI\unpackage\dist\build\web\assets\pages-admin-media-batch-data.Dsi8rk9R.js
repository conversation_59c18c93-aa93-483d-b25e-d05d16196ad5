import{_ as t,v as a,V as s,o as e,c as i,w as r,h as c,j as l,t as o,k as d,A as n,B as u,F as h,i as w,l as g,n as m,am as f,R as p,T as b,x as _}from"./index-BcNPWRTq.js";import{g as v}from"./batch.BeoHgz8a.js";import{a as S,b as D}from"./user-batch-record.uXfk0sYF.js";import{m as R}from"./media-common.vuYY10Yk.js";import{a as C,b as A}from"./admin-helpers.Cc_ABfSf.js";const y=t({name:"BatchDataPage",components:{BatchDataDisplay:t({name:"BatchDataDisplay",mixins:[R],props:{batchId:{type:String,required:!0},batchData:{type:Object,default:()=>({})}},data:()=>({viewingUsers:[],currentTab:"watched",currentTabIndex:0,tabsList:[{name:"已观看"},{name:"未观看"}],viewStatistics:{totalViews:0,totalUsers:0,completedUsers:0,completionRate:0,totalReward:0},answerStatistics:{totalAnswers:0,answerRate:0,correctRate:0},loading:!1}),computed:{filteredUsers(){let t=[...this.viewingUsers];return"watched"===this.currentTab?t=t.filter((t=>t.progress>0)):"unwatched"===this.currentTab&&(t=t.filter((t=>0===t.progress))),t}},async mounted(){this.batchId&&await this.loadRealData()},watch:{batchId:{async handler(t,a){t&&t!==a&&await this.loadRealData()}}},methods:{async loadRealData(){if(this.batchId&&!this.loading){this.loading=!0;try{const[t,a]=await Promise.all([this.loadNewBatchStatistics(),this.loadNewBatchRecords()]);if(t&&a)return void console.log("使用新API加载数据成功:",{batchStats:t,batchRecords:a});console.log("新API失败，回退到旧API"),await this.loadViewRecords(),this.batchData.statistics||await Promise.all([this.loadViewStatistics(),this.loadAnswerStatistics()])}catch(t){console.error("加载真实数据失败:",t),a({title:"数据加载失败",icon:"none"})}finally{this.loading=!1}}},async loadViewRecords(){var t;try{const a=await getBatchViewRecords(this.batchId,{pageIndex:1,pageSize:50});if(a.success&&a.data)return this.viewingUsers=(null==(t=a.data.items)?void 0:t.map((t=>({id:t.userId,name:t.userName||t.userNickname||`用户${t.userId}`,avatar:t.userAvatar?this.buildCompleteFileUrl(t.userAvatar):"/static/images/avatar-placeholder.png",viewTime:this.formatDate(t.startTime),progress:Math.round(100*(t.watchProgress||0)),reward:t.rewardAmount||0}))))||[],a.data}catch(a){return console.error("加载观看记录失败:",a),null}},async loadViewStatistics(){try{const t=await getViewStatistics({batchId:this.batchId});if(t.success&&t.data)return this.viewStatistics={totalViews:t.data.totalViews||0,totalUsers:t.data.totalUsers||0,completedUsers:t.data.completedUsers||0,completionRate:t.data.completionRate||0,totalReward:t.data.totalReward||0},t.data}catch(t){return console.error("加载观看统计失败:",t),null}},async loadAnswerStatistics(){try{const t=await getAnswerStatistics(this.batchId);if(t.success&&t.data)return this.answerStatistics={totalAnswers:t.data.totalAnswers||0,answerRate:t.data.answerRate||0,correctRate:t.data.correctRate||0},t.data}catch(t){return console.error("加载答题统计失败:",t),null}},async loadNewBatchStatistics(){try{const t=await S(this.batchId);if(t.success&&t.data)return this.viewStatistics={totalViews:t.data.viewerCount,totalUsers:t.data.totalParticipants,completedUsers:t.data.completedViewerCount,completionRate:t.data.completeRate,totalReward:t.data.totalRewardAmount},this.answerStatistics={totalAnswers:t.data.answerCount,answerRate:t.data.answerRate,correctRate:t.data.averageCorrectRate},this.batchData.statistics={totalCount:t.data.totalParticipants,viewCount:t.data.viewerCount,completeViewCount:t.data.completedViewerCount,completeRate:t.data.completeRate,totalAnswerCount:t.data.answerCount,rewardCount:t.data.rewardCount,rewardAmount:t.data.totalRewardAmount},console.log("批次统计数据映射完成:",{"原始数据":t.data,"映射后":this.batchData.statistics,viewStatistics:this.viewStatistics,answerStatistics:this.answerStatistics}),t.data}catch(t){return console.error("加载新批次统计失败:",t),null}},async loadNewBatchRecords(){try{const t=await D(this.batchId);if(t.success&&t.data)return this.viewingUsers=t.data.map((t=>({id:t.userId,name:t.userNickname||`用户${t.userId}`,avatar:t.userAvatar?this.buildCompleteFileUrl(t.userAvatar):"/static/images/avatar-placeholder.png",viewTime:this.formatDate(t.startTime),progress:Math.round(t.watchProgressPercent||0),reward:t.rewardAmount||0,isCompleted:t.isCompleted,hasAnswered:t.hasAnswered,correctRate:t.correctRate||0,rewardStatus:t.rewardStatus,rewardStatusText:t.rewardStatusText}))),t.data}catch(t){return console.error("加载新批次记录失败:",t),null}},formatDate:t=>t?"string"==typeof t?t:t.toLocaleString("zh-CN",{month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}):"未观看",onTabChange(t){this.currentTabIndex=t;this.currentTab={0:"watched",1:"unwatched"}[t]},getTotalStudents(){return this.batchData.statistics?this.batchData.statistics.totalCount||this.batchData.statistics.activeCount||0:this.viewStatistics.totalUsers||this.batchData.totalStudents||this.batchData.totalViews||this.viewingUsers.length||0},getWatchingStudents(){if(this.batchData.statistics){const t=this.batchData.statistics.viewCount||0;return console.log("观看学员数计算:",{"使用数据源":"batchData.statistics",viewCount:this.batchData.statistics.viewCount,"结果":t}),t}const t=this.viewStatistics.totalViews||this.viewingUsers.filter((t=>t.progress>0)).length;return console.log("观看学员数计算:",{"使用数据源":"viewStatistics/viewingUsers",totalViews:this.viewStatistics.totalViews,filteredUsers:this.viewingUsers.filter((t=>t.progress>0)).length,"结果":t}),t},getWatchingRate(){const t=this.getTotalStudents(),a=this.getWatchingStudents();if(0===t)return 0;const s=Math.round(a/t*100*100)/100;return console.log("观看率计算:",{total:t,watching:a,rate:s}),s},getCompletedCount(){return this.batchData.statistics?this.batchData.statistics.completeViewCount||this.batchData.statistics.completedCount||0:this.viewStatistics.completedUsers||this.viewingUsers.filter((t=>100===t.progress)).length},getCompletionRate(){if(this.batchData.statistics&&void 0!==this.batchData.statistics.completeRate)return Math.round(100*this.batchData.statistics.completeRate*100)/100;if(this.viewStatistics.completionRate>0)return Math.round(100*this.viewStatistics.completionRate)/100;const t=this.getWatchingStudents(),a=this.getCompletedCount();return 0===t?0:Math.round(a/t*100*100)/100},getAnswerStudents(){return this.batchData.statistics&&this.batchData.statistics.totalAnswerCount||0},getAnswerRate(){if(void 0!==this.answerStatistics.answerRate)return Math.round(100*this.answerStatistics.answerRate)/100;const t=this.getWatchingStudents(),a=this.getAnswerStudents();return 0===t?0:Math.round(a/t*100*100)/100},getRedPacketCount(){return this.batchData.statistics?this.batchData.statistics.rewardCount||0:this.getCompletedCount()},viewUserDetail(t){s({url:`/pages/admin/users/info?userId=${t.id}`})},calculateTotalReward(){return this.batchData.statistics&&void 0!==this.batchData.statistics.rewardAmount?this.batchData.statistics.rewardAmount.toFixed(2):void 0!==this.batchData.redPacketAmount?this.batchData.redPacketAmount.toFixed(2):this.viewStatistics.totalReward>0?this.viewStatistics.totalReward.toFixed(2):this.viewingUsers.reduce(((t,a)=>t+(a.reward||0)),0).toFixed(2)},formatDate(t){if(!t)return"";const a=new Date(t);return`${a.getFullYear()}-${(a.getMonth()+1).toString().padStart(2,"0")}-${a.getDate().toString().padStart(2,"0")} ${a.getHours().toString().padStart(2,"0")}:${a.getMinutes().toString().padStart(2,"0")}:${a.getSeconds().toString().padStart(2,"0")}`}}},[["render",function(t,a,s,b,_,v){const S=w,D=g,R=p;return e(),i(S,{class:"batch-data-container"},{default:r((()=>[c(S,{class:"data-title-section"},{default:r((()=>[c(S,{class:"data-subtitle"},{default:r((()=>[l("数据概况")])),_:1})])),_:1}),_.loading?d("",!0):(e(),i(S,{key:0,class:"stats-grid"},{default:r((()=>[c(S,{class:"stat-card-item"},{default:r((()=>[c(S,{class:"stat-content"},{default:r((()=>[c(D,{class:"stat-value"},{default:r((()=>[l(o(v.getTotalStudents()),1)])),_:1}),c(D,{class:"stat-label"},{default:r((()=>[l("学员数 ")])),_:1})])),_:1})])),_:1}),c(S,{class:"stat-card-item"},{default:r((()=>[c(S,{class:"stat-content"},{default:r((()=>[c(D,{class:"stat-value"},{default:r((()=>[l(o(v.getWatchingStudents()),1)])),_:1}),c(D,{class:"stat-label"},{default:r((()=>[l("观看学员数 ")])),_:1})])),_:1})])),_:1}),c(S,{class:"stat-card-item"},{default:r((()=>[c(S,{class:"stat-content"},{default:r((()=>[c(D,{class:"stat-value"},{default:r((()=>[l(o(v.getWatchingRate())+"%",1)])),_:1}),c(D,{class:"stat-label"},{default:r((()=>[l("观看率 ")])),_:1})])),_:1})])),_:1}),c(S,{class:"stat-card-item"},{default:r((()=>[c(S,{class:"stat-content"},{default:r((()=>[c(D,{class:"stat-value"},{default:r((()=>[l(o(v.getCompletedCount()),1)])),_:1}),c(D,{class:"stat-label"},{default:r((()=>[l("完播学员数 ")])),_:1})])),_:1})])),_:1}),c(S,{class:"stat-card-item"},{default:r((()=>[c(S,{class:"stat-content"},{default:r((()=>[c(D,{class:"stat-value"},{default:r((()=>[l(o(v.getCompletionRate())+"%",1)])),_:1}),c(D,{class:"stat-label"},{default:r((()=>[l("完播率 ")])),_:1})])),_:1})])),_:1}),c(S,{class:"stat-card-item"},{default:r((()=>[c(S,{class:"stat-content"},{default:r((()=>[c(D,{class:"stat-value"},{default:r((()=>[l(o(v.getAnswerStudents()),1)])),_:1}),c(D,{class:"stat-label"},{default:r((()=>[l("答题学员数 ")])),_:1})])),_:1})])),_:1}),c(S,{class:"stat-card-item"},{default:r((()=>[c(S,{class:"stat-content"},{default:r((()=>[c(D,{class:"stat-value"},{default:r((()=>[l(o(v.getAnswerRate())+"%",1)])),_:1}),c(D,{class:"stat-label"},{default:r((()=>[l("答题率 ")])),_:1})])),_:1})])),_:1}),c(S,{class:"stat-card-item"},{default:r((()=>[c(S,{class:"stat-content"},{default:r((()=>[c(D,{class:"stat-value"},{default:r((()=>[l(o(v.getRedPacketCount()),1)])),_:1}),c(D,{class:"stat-label"},{default:r((()=>[l("领取红包个数 ")])),_:1})])),_:1})])),_:1}),c(S,{class:"stat-card-item"},{default:r((()=>[c(S,{class:"stat-content"},{default:r((()=>[c(D,{class:"stat-value"},{default:r((()=>[l("¥ "+o(v.calculateTotalReward()),1)])),_:1}),c(D,{class:"stat-label"},{default:r((()=>[l("领取红包金额 ")])),_:1})])),_:1})])),_:1})])),_:1})),c(S,{class:"basic-data-section"},{default:r((()=>[c(D,{class:"basic-data-title"},{default:r((()=>[l("基础数据")])),_:1})])),_:1}),c(S,{class:"filter-section"},{default:r((()=>[c(S,{class:"tabs-container"},{default:r((()=>[(e(!0),n(h,null,u(_.tabsList,((t,a)=>(e(),i(S,{key:a,class:m(["tab-item",{"tab-active":_.currentTabIndex===a}]),onClick:t=>v.onTabChange(a)},{default:r((()=>[l(o(t.name),1)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1}),_.loading?(e(),i(S,{key:1,class:"loading-container"},{default:r((()=>[c(D,{class:"loading-text"},{default:r((()=>[l("正在加载数据...")])),_:1})])),_:1})):d("",!0),_.loading?d("",!0):(e(),i(S,{key:2,class:"user-list-container"},{default:r((()=>[(e(!0),n(h,null,u(v.filteredUsers,((t,a)=>(e(),i(S,{key:a,onClick:a=>v.viewUserDetail(t),class:"user-card"},{default:r((()=>[c(S,{class:"user-item-content"},{default:r((()=>[c(S,{class:"user-avatar-component"},{default:r((()=>[c(R,{src:t.avatar,class:"user-avatar",mode:"aspectFill"},null,8,["src"])])),_:2},1024),c(S,{class:"user-info"},{default:r((()=>[c(S,{class:"user-header"},{default:r((()=>[c(S,{class:"user-name"},{default:r((()=>[l(o(t.name),1)])),_:2},1024),c(S,{class:m(["status-badge",100===t.progress?"completed":t.progress>0?"incomplete":"unwatched"])},{default:r((()=>[l(o(100===t.progress?"已完播":t.progress>0?"未完播":"未观看"),1)])),_:2},1032,["class"])])),_:2},1024),c(S,{class:"progress-section"},{default:r((()=>[c(S,{class:"progress-info"},{default:r((()=>[c(D,{class:"progress-label"},{default:r((()=>[l("观看进度")])),_:1}),c(D,{class:m(["progress-value",100===t.progress?"completed":t.progress>0?"incomplete":"unwatched"])},{default:r((()=>[l(o(t.progress)+"% ",1)])),_:2},1032,["class"])])),_:2},1024),c(S,{class:"progress-component"},{default:r((()=>[c(S,{class:"progress-bar"},{default:r((()=>[c(S,{class:"progress-fill",style:f({width:t.progress+"%",backgroundColor:100===t.progress?"#1890ff":t.progress>0?"#fa8c16":"#d9d9d9"})},null,8,["style"])])),_:2},1024)])),_:2},1024)])),_:2},1024),c(S,{class:"user-details"},{default:r((()=>[c(S,{class:"detail-item"},{default:r((()=>[c(D,{class:"detail-label"},{default:r((()=>[l("观看时间")])),_:1}),c(D,{class:"detail-value"},{default:r((()=>[l(o(v.formatDate(t.viewTime)),1)])),_:2},1024)])),_:2},1024),c(S,{class:"detail-item"},{default:r((()=>[c(D,{class:"detail-label"},{default:r((()=>[l("获得奖励")])),_:1}),c(D,{class:"detail-value reward"},{default:r((()=>[l(o(t.reward.toFixed(2))+"元",1)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1024)])),_:2},1032,["onClick"])))),128)),0===v.filteredUsers.length?(e(),i(S,{key:0,class:"empty-container"},{default:r((()=>[c(D,{class:"empty-text"},{default:r((()=>[l("暂无观看用户")])),_:1})])),_:1})):d("",!0)])),_:1}))])),_:1})}],["__scopeId","data-v-de67ea4e"]])},data:()=>({batchId:"",batchData:{id:"",title:"",totalViews:0,totalReward:0,totalStudents:0}}),async onLoad(t){const{id:a}=t??{};a?(this.batchId=a,await this.loadBatchData()):C("批次ID缺失")},methods:{async loadBatchData(){const t=await A((()=>v(this.batchId)),"加载批次数据...","加载批次数据失败");if(!t.success||!t.data)throw new Error(t.msg||"获取批次数据失败");{const a=t.data;this.batchData={id:a.id,title:a.name??a.title,totalViews:a.currentParticipants??0,totalReward:a.rewardAmount??0,totalStudents:a.totalStudents??a.currentParticipants??0,redPacketAmount:a.redPacketAmount??0,statistics:a.statistics??null}}},goBack(){b()}}},[["render",function(t,a,s,l,o,d){const n=_("BatchDataDisplay"),u=w;return e(),i(u,{class:"data-page"},{default:r((()=>[c(n,{batchId:o.batchId,batchData:o.batchData},null,8,["batchId","batchData"])])),_:1})}],["__scopeId","data-v-ab0de609"]]);export{y as default};

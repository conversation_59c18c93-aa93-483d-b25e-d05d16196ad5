/**
 * 微信登录调试工具
 * 帮助诊断微信登录相关问题
 */

import wechatUserService from './wechatUserService.js'
import wechatWebAuth from './wechatWebAuth.js'
import { checkUserTokenStatus } from '../api/wechat.js'

/**
 * 微信登录调试器
 */
class WechatDebugger {
  /**
   * 检查微信登录状态
   * @returns {Object} 登录状态信息
   */
  checkLoginStatus() {
    const userInfo = wechatUserService.getUserInfo()
    const userToken = wechatUserService.getUserToken()
    const isLoggedIn = wechatUserService.isLoggedIn()

    return {
      isLoggedIn,
      hasUserInfo: !!userInfo,
      hasUserToken: !!userToken,
      userInfo: userInfo ? {
        id: userInfo.id,
        nickname: userInfo.nickname,
        avatar: userInfo.avatar,
        openId: userInfo.openId,
        employeeId: userInfo.employeeId
      } : null,
      tokenLength: userToken ? userToken.length : 0
    }
  }

  /**
   * 检查环境信息
   * @returns {Object} 环境信息
   */
  checkEnvironment() {
    return {
      isH5: wechatWebAuth.isH5Environment(),
      isWechatBrowser: wechatWebAuth.isWechatBrowser(),
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'N/A',
      currentUrl: typeof window !== 'undefined' ? window.location.href : 'N/A',
      hasAppConfig: !!(typeof window !== 'undefined' && window.APP_CONFIG),
      apiBaseUrl: typeof window !== 'undefined' && window.APP_CONFIG ? window.APP_CONFIG.apiBaseUrl : 'N/A'
    }
  }

  /**
   * 检查微信配置
   * @returns {Object} 微信配置信息
   */
  checkWechatConfig() {
    const config = typeof window !== 'undefined' && window.APP_CONFIG ? window.APP_CONFIG.wechat : null
    
    return {
      hasConfig: !!config,
      appId: config?.appId || 'N/A',
      scope: config?.scope || 'N/A',
      redirectUri: config?.redirectUri || 'N/A',
      authBaseUrl: config?.authBaseUrl || 'N/A'
    }
  }

  /**
   * 检查Token有效性
   * @returns {Promise<Object>} Token状态信息
   */
  async checkTokenStatus() {
    try {
      const userToken = wechatUserService.getUserToken()
      if (!userToken) {
        return {
          hasToken: false,
          isValid: false,
          error: '没有Token'
        }
      }

      const response = await checkUserTokenStatus()
      
      return {
        hasToken: true,
        isValid: response.success && response.data?.isValid,
        tokenInfo: response.data || null,
        error: response.success ? null : (response.message || '检查失败')
      }
    } catch (error) {
      return {
        hasToken: !!wechatUserService.getUserToken(),
        isValid: false,
        error: error.message || '检查Token状态失败'
      }
    }
  }

  /**
   * 生成调试报告
   * @returns {Promise<Object>} 完整的调试报告
   */
  async generateDebugReport() {
    const loginStatus = this.checkLoginStatus()
    const environment = this.checkEnvironment()
    const wechatConfig = this.checkWechatConfig()
    const tokenStatus = await this.checkTokenStatus()

    return {
      timestamp: new Date().toISOString(),
      loginStatus,
      environment,
      wechatConfig,
      tokenStatus,
      recommendations: this.generateRecommendations(loginStatus, environment, wechatConfig, tokenStatus)
    }
  }

  /**
   * 生成问题诊断建议
   * @param {Object} loginStatus 登录状态
   * @param {Object} environment 环境信息
   * @param {Object} wechatConfig 微信配置
   * @param {Object} tokenStatus Token状态
   * @returns {Array<string>} 建议列表
   */
  generateRecommendations(loginStatus, environment, wechatConfig, tokenStatus) {
    const recommendations = []

    // 检查登录状态
    if (!loginStatus.isLoggedIn) {
      if (!loginStatus.hasUserInfo) {
        recommendations.push('用户信息缺失，需要重新登录')
      }
      if (!loginStatus.hasUserToken) {
        recommendations.push('用户Token缺失，需要重新登录')
      }
    }

    // 检查环境
    if (!environment.isH5) {
      recommendations.push('当前不是H5环境，微信网页授权可能无法正常工作')
    }

    if (!environment.isWechatBrowser) {
      recommendations.push('当前不在微信浏览器中，建议在微信中打开')
    }

    if (!environment.hasAppConfig) {
      recommendations.push('应用配置文件未加载，请检查 /static/config/app-config.js')
    }

    // 检查微信配置
    if (!wechatConfig.hasConfig) {
      recommendations.push('微信配置缺失，请检查 app-config.js 中的 wechat 配置')
    } else {
      if (!wechatConfig.appId || wechatConfig.appId === 'N/A') {
        recommendations.push('微信AppId未配置')
      }
      if (!wechatConfig.redirectUri || wechatConfig.redirectUri === 'N/A') {
        recommendations.push('微信回调地址未配置')
      }
    }

    // 检查Token状态
    if (tokenStatus.hasToken && !tokenStatus.isValid) {
      recommendations.push('Token已过期或无效，需要重新登录')
    }

    if (recommendations.length === 0) {
      recommendations.push('配置看起来正常，如果仍有问题请检查网络连接和后端服务')
    }

    return recommendations
  }

  /**
   * 清除所有登录信息
   */
  clearLoginData() {
    wechatUserService.logout()
    console.log('已清除所有登录信息')
  }

  /**
   * 强制触发微信登录
   * @param {Object} options 登录选项
   * @returns {Promise<Object>} 登录结果
   */
  async forceLogin(options = {}) {
    try {
      // 先清除现有登录信息
      this.clearLoginData()
      
      // 触发微信登录
      const result = await wechatUserService.tryWechatAutoLogin(options)
      
      return {
        success: result.success,
        message: result.message,
        data: result.data
      }
    } catch (error) {
      return {
        success: false,
        message: error.message || '强制登录失败',
        error: error
      }
    }
  }

  /**
   * 打印调试信息到控制台
   * @param {Object} debugReport 调试报告
   */
  printDebugInfo(debugReport) {
    console.group('🔍 微信登录调试信息')
    console.log('📊 登录状态:', debugReport.loginStatus)
    console.log('🌐 环境信息:', debugReport.environment)
    console.log('⚙️ 微信配置:', debugReport.wechatConfig)
    console.log('🔑 Token状态:', debugReport.tokenStatus)
    console.log('💡 建议:', debugReport.recommendations)
    console.groupEnd()
  }
}

// 创建单例实例
const wechatDebugger = new WechatDebugger()

// 导出调试器实例和类
export default wechatDebugger
export { WechatDebugger }

// 导出常用调试方法
export const checkWechatLoginStatus = () => wechatDebugger.checkLoginStatus()
export const generateWechatDebugReport = () => wechatDebugger.generateDebugReport()
export const clearWechatLoginData = () => wechatDebugger.clearLoginData()
export const forceWechatLogin = (options) => wechatDebugger.forceLogin(options)

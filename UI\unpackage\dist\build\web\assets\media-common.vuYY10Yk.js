import{G as t,q as r,u as e,S as a,E as o,I as i,V as s,T as n,p as u}from"./index-BcNPWRTq.js";class c{static formatDuration(t,r=!1){if(!t||t<0)return r?"00:00:00":"00:00";const e=Math.floor(t/3600),a=Math.floor(t%3600/60),o=Math.floor(t%60);return r||e>0?`${e.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}:${o.toString().padStart(2,"0")}`:`${a.toString().padStart(2,"0")}:${o.toString().padStart(2,"0")}`}static formatDetailedDuration(t){if(!t||t<0)return"0秒";const r=Math.floor(t/3600),e=Math.floor(t%3600/60),a=Math.floor(t%60);return r>0?`${r}小时${e}分${a}秒`:e>0?`${e}分${a}秒`:`${a}秒`}static parseDurationToSeconds(t){if(!t)return 0;const r=t.split(":");if(2===r.length){return 60*(parseInt(r[0])||0)+(parseInt(r[1])||0)}return parseInt(t)||0}static formatDate(t){if(!t)return"";try{const r=new Date(t);if(isNaN(r.getTime()))return"";const e=r.getFullYear(),a=(r.getMonth()+1).toString().padStart(2,"0");return`${e}-${a}-${r.getDate().toString().padStart(2,"0")}`}catch(r){return console.error("日期格式化失败:",r),""}}static formatDateTime(t){if(!t)return"未设置";try{const r=new Date(t);if(isNaN(r.getTime()))return"未设置";const e=r.getFullYear(),a=(r.getMonth()+1).toString().padStart(2,"0"),o=r.getDate().toString().padStart(2,"0"),i=r.getHours().toString().padStart(2,"0");return`${e}-${a}-${o} ${i}:${r.getMinutes().toString().padStart(2,"0")}`}catch(r){return console.error("日期时间格式化失败:",r),"未设置"}}static formatDetailedDateTime(t){if(!t)return"";try{const r=new Date(t);if(isNaN(r.getTime()))return"";const e=r.getFullYear(),a=(r.getMonth()+1).toString().padStart(2,"0"),o=r.getDate().toString().padStart(2,"0"),i=r.getHours().toString().padStart(2,"0"),s=r.getMinutes().toString().padStart(2,"0");return`${e}-${a}-${o} ${i}:${s}:${r.getSeconds().toString().padStart(2,"0")}`}catch(r){return console.error("详细日期时间格式化失败:",r),""}}static formatFileSize(t){if(!t||0===t)return"0 B";const r=Math.floor(Math.log(t)/Math.log(1024));return parseFloat((t/Math.pow(1024,r)).toFixed(2))+" "+["B","KB","MB","GB","TB"][r]}static formatNumber(t){return null==t?"0":t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,",")}static formatPercentage(t,r=2){return null==t?"0.00%":(100*t).toFixed(r)+"%"}static formatAmount(t,r=2,e=""){return null==t?e+"0.00":e+t.toFixed(r)}static formatMoney(t,r="¥"){return this.formatAmount(t,2,r)}}const l=c.formatDuration,f=c.formatDate,d=c.formatDateTime,g=c.formatFileSize,S={methods:{formatDuration:t=>l(t),formatDate:t=>d(t),formatSimpleDate:t=>f(t),buildCompleteFileUrl(r){if(!r)return"";if(r.startsWith("http://")||r.startsWith("https://"))return r;const e=t();if(!e||""===e.trim())return r;return`${e.replace("/api","")}/wwwroot${r.startsWith("/")?r:`/${r}`}`},mapVideoStatus:t=>({0:"scheduled",1:"active",2:"expired"}[t]||"scheduled"),getStatusText(t){if(void 0!==t.compressionStatus){return{0:"未压缩",1:"压缩中",2:"已压缩",3:"压缩失败"}[t.compressionStatus]||"未知压缩状态"}return{expired:"已过期",scheduled:"待发布",active:"已上线"}[t.status]||"未知"},getStatusType(t){if(void 0!==t.compressionStatus){return{0:"info",1:"warning",2:"success",3:"error"}[t.compressionStatus]||"info"}return{expired:"error",scheduled:"warning",active:"success"}[t.status]||"default"},getStatusLabel:t=>({all:"全部",active:"已上线",scheduled:"待发布",expired:"已过期"}[t]||"全部"),showLoading(t="加载中..."){r({title:t,mask:!0})},hideLoading(){e()},showSuccess(t){a(t)},showError(t){o(t)},showConfirm:(t,r)=>new Promise((e=>{i({title:t,content:r,success:t=>{e(t.confirm)},fail:()=>{e(!1)}})})),safeNavigateTo(t,r={}){s({url:t,...r,fail:t=>{console.error("页面跳转失败:",t),this.showError("页面跳转失败")}})},safeNavigateBack(){n({fail:t=>{console.error("页面返回失败:",t),u({url:"/pages/admin/media/index"})}})}}};export{g as a,d as b,f,S as m};

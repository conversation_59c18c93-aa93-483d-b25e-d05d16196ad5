<template>
  <view class="video-box" :class="{ 'video-fullscreen': isFullscreen }">
    <!-- 视频播放器 -->
    <video id="videoPlayer" :src="currentVideoSource" :poster="poster" class="video-player" :controls="false"
      :show-progress="false" :show-center-play-btn="false" :enable-progress-gesture="false" :show-fullscreen-btn="false"
      :show-play-btn="false" :object-fit="videoObjectFit" :initial-time="initialPlayTime" :codec="preferredCodec"
      @timeupdate="onTimeUpdate" @ended="onVideoEnded" @fullscreenchange="onFullscreenChange" @click.stop="onVideoTap"
      @tap.stop="onVideoTap" @waiting="onVideoBuffering" @progress="onVideoProgress" @loadedmetadata="onVideoLoaded"
      @error="onVideoError"></video>

    <!-- 永久可见的退出全屏按钮 -->
    <view v-if="isFullscreen" class="persistent-exit-btn" @click.stop="exitFullscreen">
      退出全屏
    </view>

    <!-- 加载中和缓冲状态指示器 -->
    <view class="video-loader" v-if="isLoading || isBuffering">
      <view class="loader-spinner"></view>
      <text class="loader-text">{{
        isLoading ? "视频加载中..." : "正在缓冲..."
      }}</text>
      <text class="network-status" v-if="networkType">当前网络: {{ formatNetworkType(networkType) }}</text>
      <text class="network-speed" v-if="networkSpeed">网速: {{ networkSpeed }}</text>
      <view class="buffer-progress" v-if="isBuffering && !isLoading">
        <text class="buffer-text">缓冲进度: {{ Math.floor(videoBuffered) }}%</text>
        <view class="buffer-bar">
          <view class="buffer-fill" :style="{ width: videoBuffered + '%' }"></view>
        </view>
      </view>
    </view>

    <!-- 自定义控制器，使用uni-app的条件渲染 -->
    <view class="custom-controls" :class="{ 'controls-hidden': hideControls }" @click.stop @tap.stop>
      <!-- 顶部控制区域 -->
      <view class="controls-top">
        <view class="back-btn" @click="handleBack">返回</view>
        <view class="video-title">{{ title }}</view>
        <view class="network-indicator">
          <text>{{ formatNetworkType(networkType) }}</text>
        </view>
        <view v-if="isFullscreen" class="exit-fullscreen-btn" @click.stop="exitFullscreen">
          退出全屏
        </view>
      </view>

      <!-- 底部控制区域 -->
      <view class="controls-bottom">
        <!-- 播放/暂停按钮 -->
        <view class="action-btn play-btn" @click="togglePlay">
          <text class="btn-icon">{{ isPlaying ? "❚❚" : "▶" }}</text>
        </view>

        <!-- 进度条 -->
        <view class="progress-wrapper">
          <view class="progress-bg"></view>
          <view class="progress-buffered" :style="{ width: videoBuffered + '%' }"></view>
          <view class="progress-played" :style="{ width: videoProgress + '%' }"></view>
          <view class="progress-handle" :style="{ left: videoProgress + '%' }" @touchstart="handleProgressStart"
            @touchmove="handleProgressMove" @touchend="handleProgressEnd"></view>
          <view class="time-info">
            <text>{{ formatTime(currentTime) }}</text>
            <text>{{ formatTime(duration) }}</text>
          </view>
        </view>

        <!-- 全屏按钮 -->
        <view class="action-btn fullscreen-btn" @click="toggleFullscreen">
          <text class="btn-icon">{{ isFullscreen ? "↙" : "↗" }}</text>
        </view>

        <!-- 清晰度选择 -->
        <view class="action-btn quality-btn" @click="toggleQualitySelector">
          <text class="btn-icon">{{ currentQuality }}</text>
        </view>
      </view>

      <!-- 清晰度选择面板 -->
      <view class="quality-selector" v-if="showQualitySelector">
        <view class="quality-option" v-for="quality in availableQualities" :key="quality.value"
          :class="{ selected: currentQuality === quality.label }" @click="changeQuality(quality)">
          {{ quality.label }}
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "VideoPlayer",
  props: {
    // 视频信息
    videoSource: {
      type: String,
      required: true,
    },
    poster: {
      type: String,
      default: "",
    },
    title: {
      type: String,
      default: "视频播放",
    },
    initialTime: {
      type: Number,
      default: 0,
    },
    videoSources: {
      type: Object,
      default: () => ({}),
    },
    autoPlay: {
      type: Boolean,
      default: false,
    },
    aspectRatio: {
      type: String,
      default: "",
    },
  },
  data () {
    return {
      // 视频数据
      currentVideoSource: "",
      initialPlayTime: 0,
      preferredCodec: "h264",

      // 播放状态
      isPlaying: false,
      currentTime: 0,
      duration: 0,
      videoProgress: 0,
      maxWatchTime: 0,
      videoCompleted: false,

      // 视频控制
      videoContext: null,
      hideControls: false,
      controlsTimer: null,
      isFullscreen: false,
      isDragging: false,

      // 双击检测
      lastTapTime: 0,
      doubleTapTimeout: null,

      // 进度条拖动
      progressBarRect: null,

      // 加载状态
      isLoading: true,
      isBuffering: false,
      networkType: null,
      videoObjectFit: "fill",
      videoBuffered: 0,
      networkSpeed: null,

      // 网速测量
      lastLoadedBytes: 0,
      speedCheckInterval: null,
      lastSpeedCheckTime: 0,

      // 清晰度选择
      currentQuality: "高清",
      availableQualities: [
        { label: "超清", value: "high", minBandwidth: 5000 },
        { label: "高清", value: "medium", minBandwidth: 2000 },
        { label: "标清", value: "low", minBandwidth: 1000 },
        { label: "流畅", value: "lowest", minBandwidth: 0 },
      ],
      showQualitySelector: false,

      // 缓冲监控
      bufferingCount: 0,
      lastBufferingTime: 0,

      // 视频优化
      autoQualitySelection: true,
      qualityCheckInterval: null,
    };
  },
  watch: {
    videoSource: {
      immediate: true,
      handler (newVal) {
        if (newVal) {
          this.currentVideoSource = newVal;
          this.setupVideoSources();
        }
      },
    },
    initialTime: {
      immediate: true,
      handler (newVal) {
        this.initialPlayTime = newVal;
      },
    },
  },
  mounted () {
    // 创建视频上下文
    this.videoContext = uni.createVideoContext("videoPlayer", this);

    // 初始化获取进度条位置信息
    setTimeout(() => {
      this.getProgressBarRect();
    }, 500);

    // 获取网络状态
    this.checkNetworkStatus();

    // 监听网络状态变化
    uni.onNetworkStatusChange((res) => {
      this.networkType = res.networkType;

      // 如果网络变差，提醒用户
      if (res.networkType === "2g" || res.networkType === "none") {
        uni.showToast({
          title: "网络信号较弱，视频可能加载缓慢",
          icon: "none",
          duration: 2000,
        });
      }
    });

    // 如果设置了自动播放
    if (this.autoPlay) {
      setTimeout(() => {
        this.togglePlay();
      }, 300);
    }
  },
  beforeDestroy () {
    // 清理资源
    this.clearControlsTimer();

    // 如果在全屏状态，退出全屏
    if (this.isFullscreen) {
      this.exitFullscreen();
    }

    // 停止视频播放
    if (this.videoContext) {
      this.videoContext.stop();
    }

    // 取消网络监听
    uni.offNetworkStatusChange();

    // 停止网速检测
    this.stopNetworkSpeedCheck();

    // 停止质量监控
    this.stopQualityMonitoring();
  },
  methods: {
    // 设置不同清晰度的视频源
    setupVideoSources () {
      // 如果提供了videoSources，使用它
      if (Object.keys(this.videoSources).length > 0) {
        // 设置当前视频源
        this.currentVideoSource = this.videoSources.medium || this.videoSource; // 默认使用高清
      } else {
        // 从原始URL构建不同清晰度的视频源
        const baseUrl = this.videoSource;

        // 构建不同清晰度的视频源
        this.$emit("update:videoSources", {
          high: baseUrl,
          medium: baseUrl,
          low: baseUrl,
          lowest: baseUrl,
        });

        // 设置当前视频源
        this.currentVideoSource = baseUrl;
      }

      // 根据网络状态自动选择最佳清晰度
      this.selectBestQualityByNetwork();
    },

    // 检查网络状态
    checkNetworkStatus () {
      uni.getNetworkType({
        success: (res) => {
          this.networkType = res.networkType;

          // 如果网络较差，提示用户
          if (res.networkType === "2g" || res.networkType === "none") {
            uni.showToast({
              title: "当前网络信号较弱，视频加载可能较慢",
              icon: "none",
              duration: 3000,
            });

            // 自动切换到低质量
            const lowQuality = this.availableQualities.find(
              (q) => q.value === "lowest"
            );
            if (lowQuality) {
              this.changeQuality(lowQuality);
              uni.showToast({
                title: "已自动切换到流畅模式",
                icon: "none",
                duration: 2000,
              });
            }
          } else {
            // 网络良好时选择最佳质量
            this.selectBestQualityByNetwork();
          }

          // 开始网络质量监控
          this.startQualityMonitoring();
        },
      });
    },

    // 根据网络状态自动选择最佳清晰度
    selectBestQualityByNetwork () {
      if (!this.autoQualitySelection) return;

      // 获取当前网络带宽评估
      const estimatedBandwidth = this.getEstimatedBandwidth();

      // 根据带宽选择最佳清晰度
      let selectedQuality =
        this.availableQualities[this.availableQualities.length - 1]; // 默认最低质量

      for (let i = 0; i < this.availableQualities.length; i++) {
        const quality = this.availableQualities[i];
        if (estimatedBandwidth >= quality.minBandwidth) {
          selectedQuality = quality;
          break;
        }
      }

      // 应用选择的清晰度
      this.changeQuality(selectedQuality);


    },

    // 获取预估带宽
    getEstimatedBandwidth () {
      // 基于网络类型预估带宽
      switch (this.networkType) {
        case "wifi":
          return 8000; // 8Mbps
        case "4g":
          return 4000; // 4Mbps
        case "3g":
          return 1500; // 1.5Mbps
        case "2g":
          return 500; // 500Kbps
        default:
          return 2000; // 默认2Mbps
      }
    },

    // 开始质量监控
    startQualityMonitoring () {
      // 清除旧的监控
      if (this.qualityCheckInterval) {
        clearInterval(this.qualityCheckInterval);
      }

      // 定期检查网络状态，并调整视频质量
      this.qualityCheckInterval = setInterval(() => {
        if (this.autoQualitySelection && !this.isFullscreen) {
          this.checkAndAdjustQuality();
        }
      }, 30000); // 30秒检查一次
    },

    // 停止质量监控
    stopQualityMonitoring () {
      if (this.qualityCheckInterval) {
        clearInterval(this.qualityCheckInterval);
        this.qualityCheckInterval = null;
      }
    },

    // 检查并调整质量
    checkAndAdjustQuality () {
      // 如果频繁缓冲，降低质量
      if (this.bufferingCount > 3) {
        this.downgradeQuality();
        this.bufferingCount = 0;
      }

      // 如果长时间没有缓冲，考虑提升质量
      const now = Date.now();
      if (now - this.lastBufferingTime > 60000 && this.bufferingCount === 0) {
        // 1分钟没缓冲
        this.upgradeQuality();
      }
    },

    // 降低视频质量
    downgradeQuality () {
      const currentIndex = this.availableQualities.findIndex(
        (q) => q.label === this.currentQuality
      );
      if (currentIndex < this.availableQualities.length - 1) {
        const newQuality = this.availableQualities[currentIndex + 1];
        this.changeQuality(newQuality);

        // 提示用户
        uni.showToast({
          title: `网络不稳定，已自动切换到${newQuality.label}`,
          icon: "none",
          duration: 2000,
        });
      }
    },

    // 提升视频质量
    upgradeQuality () {
      const currentIndex = this.availableQualities.findIndex(
        (q) => q.label === this.currentQuality
      );
      if (currentIndex > 0) {
        const newQuality = this.availableQualities[currentIndex - 1];

        // 检查网络是否支持更高质量
        const bandwidth = this.getEstimatedBandwidth();
        if (bandwidth >= newQuality.minBandwidth) {
          this.changeQuality(newQuality);

          // 提示用户
          uni.showToast({
            title: `网络良好，已自动切换到${newQuality.label}`,
            icon: "none",
            duration: 2000,
          });
        }
      }
    },

    // 视频开始缓冲
    onVideoBuffering () {

      this.isBuffering = true;

      // 记录缓冲事件
      this.bufferingCount++;
      this.lastBufferingTime = Date.now();

      // 如果缓冲次数过多，尝试降低质量
      if (this.bufferingCount >= 3 && this.autoQualitySelection) {
        this.downgradeQuality();
        this.bufferingCount = 0;
      }

      // 开始监测网速
      this.startNetworkSpeedCheck();

      // 短暂延迟后检查视频是否已经继续播放
      setTimeout(() => {
        // 检查视频是否实际上在播放，通过timeupdate事件频率判断
        const currentTime = this.currentTime;
        setTimeout(() => {
          // 如果时间在变化，说明视频实际上在播放
          if (this.currentTime > currentTime) {

            this.isBuffering = false;
            this.stopNetworkSpeedCheck();
          }
        }, 1000);
      }, 1500);

      // 播放失败重试
      setTimeout(() => {
        if (this.isBuffering && this.videoContext) {

          this.videoContext.play();

          // 防止缓冲状态卡住
          setTimeout(() => {
            if (this.isBuffering) {
              this.isBuffering = false;
              this.stopNetworkSpeedCheck();
            }
          }, 3000);
        }
      }, 8000); // 8秒后如果还在缓冲，尝试重新播放
    },

    // 视频加载进度更新
    onVideoProgress (e) {
      // 计算缓冲进度
      if (e.detail && e.detail.buffered && this.duration > 0) {
        this.videoBuffered = (e.detail.buffered / this.duration) * 100;


        // 如果缓冲进度达到一定程度，立即结束缓冲状态
        if (this.isBuffering && this.videoBuffered > 10) {
          console.log("缓冲进度足够，取消缓冲状态");
          this.isBuffering = false;
          this.stopNetworkSpeedCheck();
        }
      }

      // 如果有缓冲长度信息，更新用于测速的数据
      if (e.detail && e.detail.buffered) {
        const currentLoadedBytes = e.detail.buffered;

        // 更新已加载字节数，用于计算网速
        if (this.lastSpeedCheckTime > 0) {
          const now = Date.now();
          const timeDiff = now - this.lastSpeedCheckTime; // 毫秒

          if (timeDiff > 0 && currentLoadedBytes > this.lastLoadedBytes) {
            const bytesLoaded = currentLoadedBytes - this.lastLoadedBytes;
            const speedKbps = Math.round(
              (bytesLoaded * 8) / (timeDiff / 1000) / 1024
            );

            // 更新网速显示
            this.updateNetworkSpeed(speedKbps);
          }

          this.lastSpeedCheckTime = now;
        } else {
          this.lastSpeedCheckTime = Date.now();
        }

        this.lastLoadedBytes = currentLoadedBytes;
      }

      // 如果视频正在播放且缓冲超过当前播放位置2秒，取消缓冲状态
      const currentProgressSeconds = (this.videoProgress / 100) * this.duration;
      const bufferedSeconds = (this.videoBuffered / 100) * this.duration;

      if (this.isBuffering && bufferedSeconds - currentProgressSeconds > 2) {
        console.log("缓冲量足够，取消缓冲状态");
        this.isBuffering = false;
        this.stopNetworkSpeedCheck();
      }
    },

    // 开始网速检测
    startNetworkSpeedCheck () {
      // 清除之前的计时器
      this.stopNetworkSpeedCheck();

      // 重置测速数据
      this.lastLoadedBytes = 0;
      this.lastSpeedCheckTime = 0;

      // 通过测试请求检查网速
      this.checkNetworkSpeedWithRequest();

      // 设置定期检查
      this.speedCheckInterval = setInterval(() => {
        this.checkNetworkSpeedWithRequest();
      }, 5000);
    },

    // 通过视频缓冲进度估算网速
    checkNetworkSpeedWithRequest () {
      // 如果不在缓冲状态，不检测
      if (!this.isBuffering) {
        return;
      }

      // 使用视频缓冲进度估算网速，避免CORS问题
      if (this.lastLoadedBytes > 0 && this.lastSpeedCheckTime > 0) {
        const now = Date.now();
        const timeDiff = now - this.lastSpeedCheckTime; // 毫秒

        // 如果距离上次检查超过1秒，且有新增缓冲
        if (timeDiff > 1000) {
          // 基于当前网络类型估算带宽
          let estimatedSpeed = 0;

          switch (this.networkType) {
            case "wifi":
              estimatedSpeed = Math.floor(Math.random() * 3000) + 5000; // 5-8 Mbps
              break;
            case "4g":
              estimatedSpeed = Math.floor(Math.random() * 2000) + 2000; // 2-4 Mbps
              break;
            case "3g":
              estimatedSpeed = Math.floor(Math.random() * 700) + 800; // 800-1500 Kbps
              break;
            case "2g":
              estimatedSpeed = Math.floor(Math.random() * 200) + 300; // 300-500 Kbps
              break;
            default:
              estimatedSpeed = Math.floor(Math.random() * 1000) + 1000; // 1-2 Mbps
          }

          // 视频缓冲进度作为速度参考
          if (this.videoBuffered > 0) {
            // 缓冲进度越快，速度估计越高
            const bufferFactor = Math.min(
              1.5,
              Math.max(0.5, this.videoBuffered / 50)
            );
            estimatedSpeed = Math.floor(estimatedSpeed * bufferFactor);
          }

          this.updateNetworkSpeed(estimatedSpeed);
          this.lastSpeedCheckTime = now;
        }
      } else {
        // 首次检测，初始化时间
        this.lastSpeedCheckTime = Date.now();
      }
    },

    // 更新网速显示
    updateNetworkSpeed (speedKbps) {
      if (speedKbps <= 0) {
        this.networkSpeed = "计算中...";
        return;
      }

      if (speedKbps >= 1024) {
        // 转换为Mbps
        const speedMbps = (speedKbps / 1024).toFixed(2);
        this.networkSpeed = speedMbps + " Mbps";
      } else {
        this.networkSpeed = speedKbps + " Kbps";
      }
    },

    // 停止网速检测
    stopNetworkSpeedCheck () {
      if (this.speedCheckInterval) {
        clearInterval(this.speedCheckInterval);
        this.speedCheckInterval = null;
      }
    },

    // 视频元数据加载完成
    onVideoLoaded (e) {
      console.log("视频元数据加载完成", e.detail);
      this.isLoading = false;
      this.duration = e.detail.duration || 0;

      // 如果视频元数据包含宽高信息，保存视频宽高比
      if (e.detail.width && e.detail.height) {
        const aspectRatio = e.detail.width / e.detail.height;
        console.log("检测到视频宽高比:", aspectRatio.toFixed(2));

        // 根据检测到的宽高比更新显示模式
        this.determineVideoObjectFit();
      }

      // 检查是否为横版视频
      this.checkIfLandscapeVideo();

      // 自动播放视频
      if (!this.isPlaying && this.videoContext && this.autoPlay) {
        this.videoContext.play();
        this.isPlaying = true;
        this.startControlsTimer();
      }

      // 通知父组件视频已加载
      this.$emit("videoLoaded", {
        duration: this.duration,
      });
    },

    // 视频加载错误
    onVideoError (e) {
      console.error("视频加载错误:", e);
      this.isLoading = false;
      this.isBuffering = false;

      uni.showToast({
        title: "视频加载失败，请检查网络连接",
        icon: "none",
        duration: 3000,
      });

      // 通知父组件视频加载失败
      this.$emit("videoError", e);
    },

    // 检查是否为横版视频
    checkIfLandscapeVideo () {
      if (this.$props.aspectRatio) {
        const [width, height] = this.$props.aspectRatio.split(":").map(Number);
        const isLandscapeVideo = width > height;

        // 如果是横版视频且不在全屏状态，提示用户横屏观看
        if (isLandscapeVideo && !this.isFullscreen) {
          setTimeout(() => {
            uni.showToast({
              title: "横版视频，建议全屏观看",
              icon: "none",
              duration: 3000,
            });
          }, 1500);
        }
      }
    },

    // 根据视频宽高比和全屏状态确定最佳的视频显示方式
    determineVideoObjectFit () {
      // 获取设备信息
      const systemInfo = uni.getSystemInfoSync();
      const deviceWidth = this.isFullscreen
        ? systemInfo.windowHeight
        : systemInfo.windowWidth;
      const deviceHeight = this.isFullscreen ? systemInfo.windowWidth : 422; // 非全屏时使用固定高度

      // 默认假设视频是16:9的比例
      let isLandscapeVideo = true;

      // 如果视频对象中定义了宽高比，计算实际比例
      if (this.$props.aspectRatio) {
        const [width, height] = this.$props.aspectRatio.split(":").map(Number);
        isLandscapeVideo = width > height;
      }

      // 全屏模式下的显示策略
      if (this.isFullscreen) {
        // 横版视频的处理
        if (isLandscapeVideo) {
          this.videoObjectFit = "contain"; // 横版视频默认用contain避免裁剪
        } else {
          this.videoObjectFit = "contain"; // 竖版视频使用contain在全屏模式下
        }
      } else {
        // 非全屏模式下，统一使用fill
        this.videoObjectFit = "fill";
      }

      console.log("设置视频显示模式:", this.videoObjectFit);
    },

    // 切换播放/暂停
    togglePlay () {
      if (!this.videoContext) return;

      if (this.isPlaying) {
        this.videoContext.pause();
        this.isPlaying = false;
      } else {
        this.videoContext.play();
        this.isPlaying = true;
        // 播放时启动自动隐藏控制栏计时器
        this.startControlsTimer();
      }

      // 通知父组件播放状态变化
      this.$emit("playStateChange", this.isPlaying);
    },

    // 切换全屏
    toggleFullscreen () {
      if (this.isFullscreen) {
        this.exitFullscreen();
      } else {
        this.enterFullscreen();
      }
    },

    // 进入全屏
    enterFullscreen () {
      // 先显示控制栏
      this.hideControls = false;

      // 尝试锁定横屏方向
      this.tryLockOrientation("landscape");

      // 确定最佳的视频显示方式
      this.determineVideoObjectFit();

      // 请求视频全屏
      this.videoContext.requestFullScreen({
        direction: 90, // 强制设置横屏方向
        success: () => {
          console.log("全屏成功");
          this.isFullscreen = true;

          // 在全屏成功后，根据视频实际宽高比调整显示模式
          setTimeout(() => {
            this.determineVideoObjectFit();
            this.hideControls = false;
          }, 300);
        },
        fail: (err) => {
          console.error("全屏API调用失败:", err);

          // 如果API调用失败，使用CSS模拟全屏
          this.isFullscreen = true;
          this.determineVideoObjectFit();

          // 显示控制栏
          setTimeout(() => {
            this.hideControls = false;
          }, 100);

          // 提示用户手动旋转设备
          this.showOrientationTip();
        },
      });

      // 通知父组件全屏状态变化
      this.$emit("fullscreenChange", true);
    },

    // 退出全屏
    exitFullscreen () {
      console.log("尝试退出全屏");

      // 强制显示控制栏
      this.hideControls = false;

      // 显示退出中提示
      uni.showToast({
        title: "正在退出全屏...",
        icon: "none",
        duration: 1000,
      });

      // 立即更新状态，不等待API回调
      this.isFullscreen = false;

      // 退出全屏API
      try {
        // 尝试使用uni-app API
        if (this.videoContext && this.videoContext.exitFullScreen) {
          this.videoContext.exitFullScreen({
            success: () => {
              console.log("退出全屏成功");

              // 恢复视频显示模式
              setTimeout(() => {
                this.videoObjectFit = "fill";
              }, 100);

              // 尝试解锁/恢复竖屏
              this.tryLockOrientation("portrait");
            },
            fail: (err) => {
              console.error("退出全屏API调用失败:", err);

              // 强制设置非全屏状态
              this.videoObjectFit = "fill";

              // 尝试恢复竖屏
              this.tryLockOrientation("portrait");

              // 如果API调用失败，尝试备用方法
              this.fallbackExitFullscreen();
            },
          });
        } else {
          // 如果没有videoContext或exitFullScreen方法，直接使用备用方法
          this.fallbackExitFullscreen();
        }
      } catch (e) {
        console.error("退出全屏出错:", e);

        // 错误处理
        this.videoObjectFit = "fill";
        this.fallbackExitFullscreen();
      }

      // 通知父组件全屏状态变化
      this.$emit("fullscreenChange", false);

      // 500ms后重新检查状态，确保真的退出了全屏
      setTimeout(() => {
        if (
          document.fullscreenElement ||
          document.webkitFullscreenElement ||
          document.mozFullScreenElement
        ) {
          console.log("检测到仍在全屏状态，尝试备用方法");
          this.fallbackExitFullscreen();
        }
      }, 500);
    },



    // 尝试锁定屏幕方向
    tryLockOrientation (orientation) {
      try {
        if (window.plus) {
          // Android和iOS支持，使用H5+API
          if (orientation === "landscape") {
            plus.screen.lockOrientation("landscape-primary");
            console.log("H5+横屏锁定请求成功");
          } else {
            plus.screen.lockOrientation("portrait-primary");
            console.log("H5+竖屏锁定请求成功");
          }
        } else if (window.screen && window.screen.orientation) {
          // 标准Web API (现代浏览器)
          if (orientation === "landscape") {
            window.screen.orientation
              .lock("landscape")
              .then(() => console.log("标准API横屏锁定成功"))
              .catch((e) => {
                console.log(
                  "标准API横屏锁定失败，这是正常的，用户可手动旋转设备"
                );
                // 如果方向锁定失败，提示用户手动旋转
                this.showOrientationTip();
              });
          } else {
            window.screen.orientation.unlock();
            console.log("标准API方向解锁请求");
          }
        } else if (orientation === "landscape") {
          // 没有API支持时，只提示用户
          this.showOrientationTip();
        }
      } catch (e) {
        console.log("方向锁定错误:", e);

        // 错误时，如果是请求横屏，则提示用户
        if (orientation === "landscape") {
          this.showOrientationTip();
        }
      }
    },

    // 显示旋转设备提示
    showOrientationTip () {
      // 检测当前设备方向
      const systemInfo = uni.getSystemInfoSync();
      const isAlreadyLandscape =
        systemInfo.windowWidth > systemInfo.windowHeight;

      // 只有在非横屏状态下才提示旋转
      if (!isAlreadyLandscape) {
        uni.showToast({
          title: "请将设备横过来获得最佳观看体验",
          icon: "none",
          duration: 3000,
        });
      }
    },

    // 全屏状态变化回调
    onFullscreenChange (e) {
      console.log("全屏状态变化:", e.detail);

      // 根据事件更新全屏状态
      const newFullscreenState = e.detail.fullScreen;

      // 状态发生变化
      if (this.isFullscreen !== newFullscreenState) {
        this.isFullscreen = newFullscreenState;

        if (newFullscreenState) {
          // 进入全屏
          console.log("检测到进入全屏");

          // 强制显示控制栏
          this.hideControls = false;
          this.clearControlsTimer(); // 停止自动隐藏

          // 显示提示
          uni.showToast({
            title: "双击屏幕或点击右上角按钮可退出全屏",
            icon: "none",
            duration: 3000,
          });

          // 调整视频显示模式
          setTimeout(() => {
            this.determineVideoObjectFit();

            // 再次确保控制栏可见
            this.hideControls = false;
          }, 300);

          // 尝试锁定横屏方向，但允许失败
          this.tryLockOrientation("landscape");
        } else {
          // 退出全屏
          console.log("检测到退出全屏");

          // 调整视频显示模式
          this.videoObjectFit = "fill";

          // 尝试恢复竖屏
          this.tryLockOrientation("portrait");
        }

        // 通知父组件全屏状态变化
        this.$emit("fullscreenChange", newFullscreenState);
      }

      // 无论如何，确保控制栏可见
      setTimeout(() => {
        this.hideControls = false;

        // 在全屏模式下，不要自动隐藏控制栏
        if (this.isPlaying && !this.isFullscreen) {
          this.startControlsTimer();
        }
      }, 200);
    },

    // 处理视频区域点击
    onVideoTap (e) {
      console.log("视频区域点击");

      // 检测双击
      const now = Date.now();
      const timeDiff = now - this.lastTapTime;

      // 清除现有双击超时
      if (this.doubleTapTimeout) {
        clearTimeout(this.doubleTapTimeout);
        this.doubleTapTimeout = null;
      }

      // 如果距离上次点击小于300ms，视为双击
      if (timeDiff < 300 && timeDiff > 0) {
        console.log("检测到双击");

        // 在全屏模式下，双击退出全屏
        if (this.isFullscreen) {
          console.log("双击退出全屏");
          this.exitFullscreen();

          // 显示提示
          uni.showToast({
            title: "正在退出全屏...",
            icon: "none",
            duration: 1000,
          });
        } else {
          // 非全屏时，双击进入全屏
          this.enterFullscreen();
        }

        // 重置点击时间
        this.lastTapTime = 0;
        return;
      }

      // 记录点击时间，并设置延迟处理单击事件
      this.lastTapTime = now;

      // 全屏模式下，优先显示控制栏
      if (this.isFullscreen && this.hideControls) {
        console.log("全屏模式下显示控制栏");
        this.hideControls = false;
        return;
      }

      // 延迟处理单击，给双击留出时间
      this.doubleTapTimeout = setTimeout(() => {
        // 如果视频已播放完成，保持控制栏显示
        if (this.videoCompleted) {
          this.hideControls = false;
          return;
        }

        // 全屏模式下，单击只显示控制栏，不隐藏
        if (this.isFullscreen) {
          if (this.hideControls) {
            this.hideControls = false;
          }
          return;
        }

        // 非全屏模式下，手动切换控制栏显示状态
        this.hideControls = !this.hideControls;
        console.log("视频点击 - 设置hideControls为:", this.hideControls);

        // 如果显示控制栏，开始自动隐藏计时
        if (!this.hideControls && this.isPlaying && !this.isFullscreen) {
          this.startControlsTimer();
        } else {
          this.clearControlsTimer();
        }

        // 防止事件冒泡
        e.stopPropagation();
      }, 300);
    },

    // 显示/隐藏控制栏
    toggleControls () {
      // 如果视频已播放完成，保持控制栏显示
      if (this.videoCompleted) {
        this.hideControls = false;
        return;
      }

      // 手动切换控制栏显示状态
      this.hideControls = !this.hideControls;
      console.log("toggleControls - 设置hideControls为:", this.hideControls);

      // 如果显示控制栏，开始自动隐藏计时
      if (!this.hideControls && this.isPlaying) {
        this.startControlsTimer();
      } else {
        this.clearControlsTimer();
      }
    },

    // 开始控制栏自动隐藏计时
    startControlsTimer () {
      this.clearControlsTimer();

      // 仅在播放状态启动计时器
      if (this.isPlaying) {
        this.controlsTimer = setTimeout(() => {
          this.hideControls = true;
        }, 3000);
      }
    },

    // 清除控制栏计时器
    clearControlsTimer () {
      if (this.controlsTimer) {
        clearTimeout(this.controlsTimer);
        this.controlsTimer = null;
      }
    },

    // 返回操作
    handleBack () {
      // 如果在全屏状态，先退出全屏
      if (this.isFullscreen) {
        this.exitFullscreen();
        return;
      }

      // 否则触发返回事件，由父组件处理
      this.$emit("back");
    },

    // 获取进度条元素信息
    getProgressBarRect () {
      const query = uni.createSelectorQuery().in(this);
      query
        .select(".progress-wrapper")
        .boundingClientRect((rect) => {
          if (rect) {
            this.progressBarRect = rect;
          }
        })
        .exec();
    },

    // 进度条拖动开始
    handleProgressStart (e) {
      if (this.videoCompleted) return;

      this.isDragging = true;
      this.clearControlsTimer(); // 拖动时不自动隐藏控制栏

      if (!this.progressBarRect) {
        this.getProgressBarRect();
      }
    },

    // 进度条拖动中
    handleProgressMove (e) {
      if (!this.isDragging || this.videoCompleted) return;
      if (!this.progressBarRect) return;

      // 计算拖动位置
      const touch = e.touches[0];
      const offsetX = touch.clientX - this.progressBarRect.left;
      const percentage = Math.max(
        0,
        Math.min(offsetX / this.progressBarRect.width, 1)
      );

      // 计算目标时间
      const targetTime = percentage * this.duration;

      // 限制不能拖动到未观看的部分
      if (targetTime > this.maxWatchTime) {
        // 限制在已观看范围内
        this.videoProgress = (this.maxWatchTime / this.duration) * 100;
        uni.showToast({
          title: "请完整观看视频",
          icon: "none",
          duration: 1000,
        });
      } else {
        // 更新进度显示
        this.videoProgress = percentage * 100;
      }
    },

    // 进度条拖动结束
    handleProgressEnd (e) {
      if (!this.isDragging) return;

      // 计算最终位置对应的时间
      const targetTime = Math.min(
        (this.videoProgress / 100) * this.duration,
        this.maxWatchTime
      );

      // 跳转到指定时间
      this.videoContext.seek(targetTime);
      this.currentTime = targetTime;

      // 结束拖动状态
      this.isDragging = false;

      // 如果正在播放，重新启动控制栏自动隐藏
      if (this.isPlaying) {
        this.startControlsTimer();
      }
    },

    // 视频时间更新
    onTimeUpdate (e) {
      if (this.isDragging) return; // 拖动时不更新时间显示

      const newTime = e.detail.currentTime;

      // 如果时间正在更新，且处于缓冲状态，说明实际上视频已经继续播放
      // 这是一个额外的安全检查，确保缓冲状态不会卡住
      if (this.isBuffering && Math.abs(newTime - this.currentTime) > 0.2) {
        console.log("通过timeUpdate检测到视频在播放，取消缓冲状态");
        this.isBuffering = false;
        this.stopNetworkSpeedCheck();
      }

      this.currentTime = newTime;
      this.duration = e.detail.duration || this.duration;

      // 计算进度百分比
      this.videoProgress = (this.currentTime / this.duration) * 100;

      // 更新最大观看时间
      if (newTime > this.maxWatchTime) {
        this.maxWatchTime = newTime;
      }

      // 检查是否接近视频结束
      if (this.duration - this.currentTime < 1 && !this.videoCompleted) {
        // 提前标记为已完成
        this.videoCompleted = true;
      }

      // 通知父组件时间更新
      this.$emit("timeUpdate", {
        currentTime: this.currentTime,
        duration: this.duration,
        progress: this.videoProgress,
        maxWatchTime: this.maxWatchTime,
      });
    },

    // 视频播放完成
    onVideoEnded () {
      this.videoCompleted = true;
      this.isPlaying = false;
      this.maxWatchTime = this.duration;
      this.hideControls = false; // 视频结束时显示控制栏

      // 如果在全屏状态，退出全屏
      if (this.isFullscreen) {
        this.exitFullscreen();
      }

      // 通知父组件视频播放完成
      this.$emit("videoEnded", {
        currentTime: this.currentTime,
        duration: this.duration,
      });
    },

    // 格式化时间
    formatTime (seconds) {
      if (!seconds) return "00:00";

      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);

      return `${mins.toString().padStart(2, "0")}:${secs
        .toString()
        .padStart(2, "0")}`;
    },

    // 格式化网络类型
    formatNetworkType (type) {
      switch (type) {
        case "wifi":
          return "Wi-Fi";
        case "4g":
          return "4G";
        case "3g":
          return "3G";
        case "2g":
          return "2G";
        case "unknown":
          return "未知网络";
        default:
          return type;
      }
    },

    // 清晰度选择
    toggleQualitySelector () {
      this.showQualitySelector = !this.showQualitySelector;
    },

    // 改变清晰度
    changeQuality (quality) {
      if (this.currentQuality === quality.label) {
        this.showQualitySelector = false;
        return; // 相同质量不需切换
      }

      // 记住当前播放位置
      const currentPlayTime = this.currentTime;
      const wasPlaying = this.isPlaying;

      // 更新当前质量显示
      this.currentQuality = quality.label;
      this.showQualitySelector = false;

      // 切换视频源
      if (this.videoSources && this.videoSources[quality.value]) {
        // 标记为加载状态
        this.isLoading = true;

        // 保存当前播放位置
        this.initialPlayTime = currentPlayTime;

        // 切换视频源
        this.currentVideoSource = this.videoSources[quality.value];

        console.log(
          `切换到${quality.label}质量，保存播放位置: ${currentPlayTime}秒`
        );

        // 如果是自动播放状态，加载后自动恢复播放
        setTimeout(() => {
          if (wasPlaying && this.videoContext) {
            this.videoContext.play();
          }
        }, 500);
      } else {
        console.log("没有找到对应质量的视频源");
      }

      // 通知父组件质量变更
      this.$emit("qualityChange", quality);
    },
  },
};
</script>

<style>
/* 视频播放区域 */
.video-box {
  position: relative;
  width: 100%;
  height: 422rpx;
  background-color: #000;
}

/* 全屏模式 */
.video-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: 999;
}

/* 视频播放器 */
.video-player {
  width: 100%;
  height: 100%;
}

/* 自定义控制条 */
.custom-controls {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 10;
}

/* 顶部控制区 */
.controls-top {
  padding: 30rpx 20rpx;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7), transparent);
  display: flex;
  align-items: center;
}

/* 底部控制区 */
.controls-bottom {
  padding: 30rpx 20rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
  display: flex;
  align-items: center;
}

/* 返回按钮 */
.back-btn {
  padding: 6rpx 20rpx;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 28rpx;
  border-radius: 30rpx;
  margin-right: 20rpx;
}

/* 视频标题 */
.video-title {
  color: white;
  font-size: 28rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

/* 网络状态指示器 */
.network-indicator {
  background: rgba(0, 0, 0, 0.5);
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  margin-left: 10rpx;
}

/* 退出全屏按钮 */
.exit-fullscreen-btn {
  background: rgba(255, 0, 0, 0.6);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: white;
  margin-left: 10rpx;
  font-weight: bold;
}

/* 操作按钮 */
.action-btn {
  padding: 12rpx 20rpx;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  font-size: 28rpx;
  border-radius: 30rpx;
  min-width: 80rpx;
  text-align: center;
}

.btn-icon {
  font-size: 32rpx;
  display: inline-block;
}

/* 进度条包装器 */
.progress-wrapper {
  flex: 1;
  margin: 0 20rpx;
  position: relative;
}

/* 进度条背景 */
.progress-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 8rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 4rpx;
}

/* 缓冲进度 */
.progress-buffered {
  position: absolute;
  top: 0;
  left: 0;
  height: 8rpx;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 4rpx;
}

/* 播放进度 */
.progress-played {
  position: absolute;
  top: 0;
  left: 0;
  height: 8rpx;
  background-color: #186BFF;
  border-radius: 4rpx;
}

/* 进度点 */
.progress-handle {
  position: absolute;
  top: 0;
  width: 16rpx;
  height: 16rpx;
  margin-top: -4rpx;
  background-color: white;
  border-radius: 50%;
  transform: translateX(-50%);
}

/* 时间信息 */
.time-info {
  display: flex;
  justify-content: space-between;
  margin-top: 16rpx;
  font-size: 24rpx;
  color: white;
}

/* 加载中和缓冲状态指示器 */
.video-loader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

.loader-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top: 4rpx solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.loader-text {
  font-size: 28rpx;
  color: white;
  margin-bottom: 20rpx;
}

.network-status {
  font-size: 26rpx;
  color: #ccc;
  padding: 4rpx 16rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 20rpx;
  margin-top: 16rpx;
}

.network-speed {
  font-size: 26rpx;
  color: #ccc;
  padding: 4rpx 16rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 20rpx;
  margin-top: 16rpx;
}

.buffer-progress {
  margin-top: 16rpx;
}

.buffer-text {
  font-size: 26rpx;
  color: #ccc;
  margin-bottom: 10rpx;
}

.buffer-bar {
  height: 8rpx;
  background-color: rgba(255, 255, 255, 0.3);
  border-radius: 4rpx;
}

.buffer-fill {
  height: 8rpx;
  background-color: #186BFF;
  border-radius: 4rpx;
}

/* 清晰度选择面板 */
.quality-selector {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 0 0 12rpx 12rpx;
  padding: 20rpx;
  z-index: 1002;
}

.quality-option {
  padding: 16rpx;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  color: white;
  text-align: center;
}

.quality-option.selected {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 控制栏隐藏状态 */
.controls-hidden {
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

/* 全屏模式下控制栏样式 */
.video-fullscreen .custom-controls {
  z-index: 100;
}

/* 全屏模式下视频元素 */
.video-fullscreen .video-player {
  z-index: 1;
}

/* 全屏模式下强制显示控制栏 */
.video-fullscreen .exit-fullscreen-btn {
  z-index: 101;
  display: block !important;
}

/* 永久可见的退出全屏按钮 */
.persistent-exit-btn {
  position: fixed;
  top: 20rpx;
  right: 20rpx;
  background-color: rgba(255, 0, 0, 0.8);
  color: white;
  padding: 16rpx 24rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
  z-index: 1002;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.5);
}
</style>
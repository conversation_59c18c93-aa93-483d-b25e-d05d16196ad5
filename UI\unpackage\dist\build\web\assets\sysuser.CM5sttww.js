import{m as s}from"./index-BcNPWRTq.js";function r(r){return s.post("/SysUser/create",r)}function t(r){return s.get(`/SysUser/${r}`)}function e(r){return s.post("/SysUser/change-password",r)}function n(r){return s.post("/SysUser/reset-password",r)}function a(r={}){return s.get("/SysUser/administrators",r)}function o(r={}){return s.get("/SysUser/employees",r)}function u(r,t={}){return s.get(`/SysUser/subordinates/${r}`,t)}function i(r,t){return e={userId:r,status:t},s.post("/SysUser/update",e);var e}export{u as a,a as b,e as c,o as d,r as e,t as g,n as r,i as t};

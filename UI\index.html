<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <script>
    var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
      CSS.supports('top: constant(a)'))
    document.write(
      '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
      (coverSupport ? ', viewport-fit=cover' : '') + '" />')
  </script>
  <title></title>
  <!-- 应用配置文件 - 可在发布后直接修改 -->
  <script src="/static/config/app-config.js?v=1.0.1"></script>
  <!-- vConsole 调试工具 -->
  <script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
  <script>
    // 初始化 vConsole
    if (typeof window !== 'undefined' && window.VConsole) {
      var vConsole = new window.VConsole();
      console.log('🔍 vConsole 调试工具已启用 - 可以查看网络请求了！');
    }
  </script>
  <!--preload-links-->
  <!--app-context-->
</head>

<body>
  <div id="app"><!--app-html--></div>
  <script type="module" src="/main.js"></script>
</body>

</html>
import{_ as e,v as t,ax as s,q as o,u as i,ay as a,o as l,c as n,w as r,h as d,n as c,j as u,t as p,k as f,ak as h,A as m,B as g,F as v,l as _,i as w,R as y,C as I,az as b,m as C,am as k,a as x,G as S,I as D,T as F,au as P,p as V,x as E,a2 as T,aA as q,aB as A}from"./index-BcNPWRTq.js";import{a as $,m as M}from"./media-common.vuYY10Yk.js";import{g as z,u as U}from"./video.RvwUpDFe.js";import{b as R,a as Q}from"./admin-helpers.Cc_ABfSf.js";const B=e({name:"VideoUploader",props:{value:{type:Object,default:()=>({path:"",thumbnail:"",duration:"",fileSize:0,bitrate:"",title:""})},isEditMode:{type:Boolean,default:!1}},data(){return{videoInfo:{...this.value},isAnalyzing:!1,showAdvancedSettings:!1,enableCompression:!1!==this.value.enableCompression,selectedQuality:this.value.compressionQuality||7,qualityOptions:[{value:10,title:"超高画质",description:"几乎无损压缩，适合专业用途",icon:"💎",recommended:!1},{value:8,title:"高画质",description:"轻微压缩，画质优秀",icon:"🌟",recommended:!1},{value:7,title:"标准画质",description:"平衡画质与文件大小",icon:"⚖️",recommended:!0},{value:5,title:"经济画质",description:"适度压缩，节省空间",icon:"💰",recommended:!1},{value:3,title:"极简画质",description:"最大压缩，快速传输",icon:"🚀",recommended:!1}]}},watch:{value:{handler(e){e&&(this.videoInfo={...this.videoInfo,...e,path:this.videoInfo.path||e.path||"",thumbnail:this.videoInfo.thumbnail||e.thumbnail||"",duration:this.videoInfo.duration||e.duration||"",fileSize:this.videoInfo.fileSize||e.fileSize||0},this.enableCompression=!1!==e.enableCompression,this.selectedQuality=e.compressionQuality||7)},deep:!0}},methods:{chooseVideo(){this.isEditMode?t({title:"编辑模式下不能更换视频",icon:"none"}):s({count:1,sourceType:["album","camera"],maxDuration:600,success:e=>{e&&e.tempFilePath&&this.analyzeVideo(e)},fail:()=>{t({title:"选择视频失败",icon:"none"})}})},async analyzeVideo(e){this.isAnalyzing=!0;try{if(!e||!e.tempFilePath)throw new Error("无效的视频数据");o({title:"处理视频中...",mask:!0}),this.videoInfo.path=e.tempFilePath||"",this.videoInfo.duration=this.formatDuration(e.duration||0),this.videoInfo.fileSize=void 0!==e.size?e.size:0,this.processBasicVideoInfo(e),this.setVideoThumbnail(e),this.validateVideoBasic(),this.$emit("input",{...this.value,path:this.videoInfo.path,thumbnail:this.videoInfo.thumbnail,duration:this.videoInfo.duration,fileSize:this.videoInfo.fileSize,resolution:this.videoInfo.resolution,format:this.videoInfo.format,bitrate:this.videoInfo.bitrate,fps:this.videoInfo.fps}),await new Promise((e=>setTimeout(e,500))),i(),t({title:"视频处理完成",icon:"success"})}catch(s){i(),t({title:"视频处理失败",icon:"none"})}finally{this.isAnalyzing=!1}},processBasicVideoInfo(e){if(e.duration&&e.duration>0&&e.size){const t=Math.round(8*e.size/e.duration/1e3);this.videoInfo.bitrate=`${t} kbps`}else this.videoInfo.bitrate="未知"},setVideoThumbnail(e){e.thumbTempFilePath?this.videoInfo.thumbnail=e.thumbTempFilePath:this.videoInfo.thumbnail=""},useDefaultBase64Cover(){try{const e=document.createElement("canvas"),t=e.getContext("2d");e.width=320,e.height=180;const s=t.createLinearGradient(0,0,320,180);s.addColorStop(0,"#667eea"),s.addColorStop(1,"#764ba2"),t.fillStyle=s,t.fillRect(0,0,320,180),t.fillStyle="rgba(255, 255, 255, 0.8)",t.beginPath(),t.moveTo(120,60),t.lineTo(120,120),t.lineTo(180,90),t.closePath(),t.fill(),t.fillStyle="white",t.font="16px Arial",t.textAlign="center",t.fillText("Video Cover",160,150),t.font="12px Arial",t.fillText(`Duration: ${this.videoInfo.duration}`,160,170),this.videoInfo.thumbnail=e.toDataURL("image/jpeg",.8)}catch(e){this.videoInfo.thumbnail="data:image/svg+xml;charset=utf-8,"+encodeURIComponent('\n          <svg width="320" height="180" xmlns="http://www.w3.org/2000/svg">\n            <rect width="320" height="180" fill="#667eea"/>\n            <polygon points="120,60 120,120 180,90" fill="white" opacity="0.8"/>\n          </svg>\n        ')}},validateVideoBasic(){this.videoInfo.fileSize>2147483648&&t({title:`文件过大(${this.formatFileSize(this.videoInfo.fileSize)})，最大支持2GB`,icon:"none",duration:3e3})},formatFileSize:$,formatDuration:e=>`${Math.floor(e/60)}:${Math.floor(e%60).toString().padStart(2,"0")}`,chooseCoverImage(){a({count:1,sizeType:["compressed"],sourceType:["album","camera"],success:e=>{this.videoInfo.thumbnail=e.tempFilePaths[0],this.$emit("input",{...this.value,thumbnail:this.videoInfo.thumbnail}),t({title:"封面设置成功",icon:"success"})},fail:()=>{t({title:"选择图片失败",icon:"none"})}})},toggleAdvancedSettings(){this.showAdvancedSettings=!this.showAdvancedSettings},onCompressionToggle(e){this.enableCompression=e.detail.value,this.updateVideoInfo()},selectQuality(e){this.selectedQuality=e,this.updateVideoInfo()},updateVideoInfo(){const e={...this.value,enableCompression:this.enableCompression,compressionQuality:this.selectedQuality};this.$emit("input",e)},getCompressionDescription(){if(!this.enableCompression)return"不压缩，保持原始文件";const e=this.qualityOptions.find((e=>e.value===this.selectedQuality));return e?e.description:"标准压缩"}}},[["render",function(e,t,s,o,i,a){const C=_,k=w,x=y,S=I,D=b;return l(),n(k,{class:"video-uploader"},{default:r((()=>[d(k,{class:"upload-container"},{default:r((()=>[d(k,{class:c(["upload-area",{disabled:s.isEditMode,analyzing:i.isAnalyzing}]),onClick:a.chooseVideo},{default:r((()=>[i.videoInfo.path?(l(),n(k,{key:1,class:"upload-preview"},{default:r((()=>[d(x,{src:i.videoInfo.thumbnail,mode:"aspectFill"},null,8,["src"]),d(C,{class:"video-duration"},{default:r((()=>[u(p(i.videoInfo.duration),1)])),_:1}),d(k,{class:"preview-actions",onClick:t[0]||(t[0]=h((()=>{}),["stop"]))},{default:r((()=>[s.isEditMode?f("",!0):(l(),n(S,{key:0,type:"button",class:"action-btn",onClick:h(a.chooseVideo,["stop"])},{default:r((()=>[d(C,{class:"iconfont icon-refresh"}),u(" 重新选择 ")])),_:1},8,["onClick"]))])),_:1}),s.isEditMode?(l(),n(k,{key:0,class:"preview-mask edit-mode"},{default:r((()=>[d(C,{class:"preview-text"},{default:r((()=>[u("视频不可更换")])),_:1})])),_:1})):f("",!0)])),_:1})):(l(),n(k,{key:0,class:"upload-placeholder"},{default:r((()=>[d(k,{class:"upload-icon-container"},{default:r((()=>[d(C,{class:c(["upload-icon",{rotating:i.isAnalyzing}])},{default:r((()=>[u("🎬")])),_:1},8,["class"])])),_:1}),d(C,{class:"upload-text"},{default:r((()=>[u(p(i.isAnalyzing?"正在分析视频...":s.isEditMode?"视频不可更换":"拖拽或点击上传视频"),1)])),_:1}),d(C,{class:"upload-desc"},{default:r((()=>[u(p(s.isEditMode?"编辑模式下只能修改视频信息":"支持 MP4、MOV、AVI、MKV 格式，最大 2GB"),1)])),_:1}),s.isEditMode?f("",!0):(l(),n(k,{key:0,class:"upload-features"},{default:r((()=>[d(k,{class:"feature-item"},{default:r((()=>[d(C,{class:"feature-icon"},{default:r((()=>[u("⚡")])),_:1}),d(C,{class:"feature-text"},{default:r((()=>[u("快速上传")])),_:1})])),_:1}),d(k,{class:"feature-item"},{default:r((()=>[d(C,{class:"feature-icon"},{default:r((()=>[u("🎯")])),_:1}),d(C,{class:"feature-text"},{default:r((()=>[u("智能分析")])),_:1})])),_:1}),d(k,{class:"feature-item"},{default:r((()=>[d(C,{class:"feature-icon"},{default:r((()=>[u("🔒")])),_:1}),d(C,{class:"feature-text"},{default:r((()=>[u("安全存储")])),_:1})])),_:1})])),_:1}))])),_:1}))])),_:1},8,["onClick","class"]),i.videoInfo.path&&!s.isEditMode?(l(),n(k,{key:0,class:"advanced-settings"},{default:r((()=>[d(k,{class:"settings-header",onClick:a.toggleAdvancedSettings},{default:r((()=>[d(C,{class:"settings-title"},{default:r((()=>[u("高级设置")])),_:1}),d(C,{class:"settings-toggle"},{default:r((()=>[u(p(i.showAdvancedSettings?"收起":"展开"),1)])),_:1}),d(C,{class:"settings-icon"},{default:r((()=>[u(p(i.showAdvancedSettings?"▲":"▼"),1)])),_:1})])),_:1},8,["onClick"]),i.showAdvancedSettings?(l(),n(k,{key:0,class:"compression-panel"},{default:r((()=>[d(k,{class:"panel-header"},{default:r((()=>[d(C,{class:"panel-title"},{default:r((()=>[u("🎯 智能压缩")])),_:1}),d(C,{class:"panel-desc"},{default:r((()=>[u("上传后自动压缩，节省存储空间和传输时间")])),_:1})])),_:1}),d(k,{class:"compression-toggle"},{default:r((()=>[d(k,{class:"toggle-item"},{default:r((()=>[d(C,{class:"toggle-label"},{default:r((()=>[u("启用压缩")])),_:1}),d(D,{checked:i.enableCompression,onChange:a.onCompressionToggle,color:"#186BFF"},null,8,["checked","onChange"])])),_:1}),d(C,{class:"toggle-desc"},{default:r((()=>[u("开启后将在服务器端自动压缩视频")])),_:1})])),_:1}),i.enableCompression?(l(),n(k,{key:0,class:"compression-quality"},{default:r((()=>[d(C,{class:"quality-label"},{default:r((()=>[u("压缩质量")])),_:1}),d(k,{class:"quality-options"},{default:r((()=>[(l(!0),m(v,null,g(i.qualityOptions,(e=>(l(),n(k,{class:c(["quality-option",{active:i.selectedQuality===e.value}]),key:e.value,onClick:t=>a.selectQuality(e.value)},{default:r((()=>[d(k,{class:"option-icon"},{default:r((()=>[u(p(e.icon),1)])),_:2},1024),d(k,{class:"option-content"},{default:r((()=>[d(C,{class:"option-title"},{default:r((()=>[u(p(e.title),1)])),_:2},1024),d(C,{class:"option-desc"},{default:r((()=>[u(p(e.description),1)])),_:2},1024)])),_:2},1024),e.recommended?(l(),n(k,{key:0,class:"option-badge"},{default:r((()=>[u("推荐")])),_:1})):f("",!0)])),_:2},1032,["class","onClick"])))),128))])),_:1})])),_:1})):f("",!0),i.enableCompression?(l(),n(k,{key:1,class:"compression-info"},{default:r((()=>[d(k,{class:"info-item"},{default:r((()=>[d(C,{class:"info-icon"},{default:r((()=>[u("⚡")])),_:1}),d(C,{class:"info-text"},{default:r((()=>[u("压缩将在上传完成后自动开始")])),_:1})])),_:1}),d(k,{class:"info-item"},{default:r((()=>[d(C,{class:"info-icon"},{default:r((()=>[u("📱")])),_:1}),d(C,{class:"info-text"},{default:r((()=>[u("您可以关闭页面，压缩在后台进行")])),_:1})])),_:1}),d(k,{class:"info-item"},{default:r((()=>[d(C,{class:"info-icon"},{default:r((()=>[u("🔔")])),_:1}),d(C,{class:"info-text"},{default:r((()=>[u("压缩完成后可在视频管理中查看")])),_:1})])),_:1})])),_:1})):f("",!0)])),_:1})):f("",!0)])),_:1})):f("",!0),i.videoInfo.path?(l(),n(k,{key:1,class:"video-info-panel"},{default:r((()=>[d(k,{class:"panel-header"},{default:r((()=>[d(C,{class:"panel-title"},{default:r((()=>[u("视频信息")])),_:1}),d(C,{class:"panel-status success"},{default:r((()=>[u("✓ 分析完成")])),_:1})])),_:1}),d(k,{class:"info-grid"},{default:r((()=>[d(k,{class:"info-item"},{default:r((()=>[d(C,{class:"info-label"},{default:r((()=>[u("文件大小")])),_:1}),d(C,{class:"info-value"},{default:r((()=>[u(p(a.formatFileSize(i.videoInfo.fileSize)),1)])),_:1})])),_:1}),d(k,{class:"info-item"},{default:r((()=>[d(C,{class:"info-label"},{default:r((()=>[u("视频时长")])),_:1}),d(C,{class:"info-value"},{default:r((()=>[u(p(i.videoInfo.duration),1)])),_:1})])),_:1}),d(k,{class:"info-item"},{default:r((()=>[d(C,{class:"info-label"},{default:r((()=>[u("比特率")])),_:1}),d(C,{class:"info-value"},{default:r((()=>[u(p(i.videoInfo.bitrate||"计算中..."),1)])),_:1})])),_:1}),d(k,{class:"info-item"},{default:r((()=>[d(C,{class:"info-label"},{default:r((()=>[u("状态")])),_:1}),d(C,{class:"info-value status-good"},{default:r((()=>[u("✓ 符合要求")])),_:1})])),_:1})])),_:1})])),_:1})):f("",!0),i.videoInfo.path&&!s.isEditMode?(l(),n(k,{key:2,class:"cover-upload-section"},{default:r((()=>[d(k,{class:"section-header"},{default:r((()=>[d(C,{class:"section-title"},{default:r((()=>[u("视频封面 "),d(C,{class:"required-mark"},{default:r((()=>[u("*")])),_:1})])),_:1}),d(C,{class:"section-desc"},{default:r((()=>[u("请为你的视频选择一个吸引人的封面（必填）")])),_:1})])),_:1}),d(k,{class:"cover-upload-area",onClick:a.chooseCoverImage},{default:r((()=>[i.videoInfo.thumbnail?(l(),n(k,{key:0,class:"cover-content"},{default:r((()=>[d(x,{src:i.videoInfo.thumbnail,mode:"aspectFill",class:"cover-preview-image"},null,8,["src"]),d(k,{class:"cover-overlay"},{default:r((()=>[d(k,{class:"overlay-content"},{default:r((()=>[d(C,{class:"overlay-icon"},{default:r((()=>[u("📷")])),_:1}),d(C,{class:"overlay-text"},{default:r((()=>[u("点击更换封面")])),_:1})])),_:1})])),_:1}),d(k,{class:"cover-status-badge"},{default:r((()=>[d(C,{class:"status-text"},{default:r((()=>[u("✓ 已设置")])),_:1})])),_:1})])),_:1})):(l(),n(k,{key:1,class:"cover-placeholder"},{default:r((()=>[d(k,{class:"placeholder-icon-container"},{default:r((()=>[d(C,{class:"placeholder-icon"},{default:r((()=>[u("🖼️")])),_:1})])),_:1}),d(C,{class:"placeholder-title"},{default:r((()=>[u("选择封面图片")])),_:1}),d(C,{class:"placeholder-subtitle"},{default:r((()=>[u("点击上传或拖拽图片到此处")])),_:1}),d(k,{class:"placeholder-features"},{default:r((()=>[d(C,{class:"feature-text"},{default:r((()=>[u("• 支持 JPG、PNG 格式")])),_:1}),d(C,{class:"feature-text"},{default:r((()=>[u("• 建议 16:9 比例")])),_:1}),d(C,{class:"feature-text"},{default:r((()=>[u("• 文件不超过 5MB")])),_:1})])),_:1})])),_:1}))])),_:1},8,["onClick"])])),_:1})):f("",!0)])),_:1})])),_:1})}],["__scopeId","data-v-87b4e9af"]]);const N=e({name:"CompressionProgress",props:{visible:{type:Boolean,default:!1},fileId:{type:String,required:!0},autoClose:{type:Boolean,default:!0}},data:()=>({progressData:{progress:0,status:"waiting",originalSize:0,compressedSize:0,compressionRatio:0,errorMessage:"",startTime:null},polling:null,canClose:!1,lastProgress:0,lastUpdateTime:null}),watch:{visible(e){e?this.startPolling():this.stopPolling()},fileId(){this.visible&&this.startPolling()}},methods:{startPolling(){this.stopPolling(),this.fetchProgress(),this.polling=setInterval((()=>{this.fetchProgress()}),1e3)},stopPolling(){this.polling&&(clearInterval(this.polling),this.polling=null)},async fetchProgress(){try{const e=await C.get(`/Video/compression-progress/${this.fileId}`);if(e.success){const t=e.data;this.progressData.progress!==t.progress&&(this.lastProgress=this.progressData.progress,this.lastUpdateTime=Date.now()),this.progressData=t,"completed"!==this.progressData.status&&"failed"!==this.progressData.status||(this.canClose=!0,this.stopPolling(),this.autoClose&&"completed"===this.progressData.status&&setTimeout((()=>{this.closeProgress()}),3e3))}}catch(e){console.error("获取压缩进度失败:",e)}},getStatusText(){switch(this.progressData.status){case"waiting":return"准备中";case"processing":return"压缩中";case"completed":return"已完成";case"failed":return"失败";default:return"未知状态"}},formatFileSize:$,getProgressDetails(){if("processing"!==this.progressData.status)return"";const e=Date.now(),t=new Date(this.progressData.startTime).getTime(),s=Math.floor((e-t)/1e3);if(this.progressData.progress>0&&s>0){const e=this.progressData.progress/s,t=100-this.progressData.progress,o=Math.floor(t/e),i=e=>{if(e<60)return`${e}秒`;return`${Math.floor(e/60)}分${e%60}秒`};return`已用时: ${i(s)} | 预计剩余: ${i(o)}`}return"正在计算预计时间..."},closeProgress(){this.$emit("close")},onMaskTap(){this.canClose&&this.closeProgress()},retryCompression(){this.$emit("retry",this.fileId),this.progressData.status="waiting",this.progressData.progress=0,this.canClose=!1,this.startPolling()}},beforeDestroy(){this.stopPolling()}},[["render",function(e,t,s,o,i,a){const h=w,m=_,g=I;return s.visible?(l(),n(h,{key:0,class:"compression-progress-container"},{default:r((()=>[d(h,{class:"progress-mask",onClick:a.onMaskTap},null,8,["onClick"]),d(h,{class:"progress-card"},{default:r((()=>[d(h,{class:"progress-header"},{default:r((()=>[d(m,{class:"progress-title"},{default:r((()=>[u("🎬 视频压缩中")])),_:1}),i.canClose?(l(),n(m,{key:0,class:"close-btn",onClick:a.closeProgress},{default:r((()=>[u("✕")])),_:1},8,["onClick"])):f("",!0)])),_:1}),d(h,{class:"progress-section"},{default:r((()=>[d(h,{class:"progress-info"},{default:r((()=>[d(m,{class:"progress-percent"},{default:r((()=>[u(p(i.progressData.progress)+"%",1)])),_:1}),d(m,{class:"progress-status"},{default:r((()=>[u(p(a.getStatusText()),1)])),_:1})])),_:1}),d(h,{class:"progress-bar"},{default:r((()=>[d(h,{class:c(["progress-fill",{"progress-active":"processing"===i.progressData.status}]),style:k({width:i.progressData.progress+"%"})},null,8,["class","style"]),d(h,{class:"progress-text"},{default:r((()=>[u(p(i.progressData.progress)+"%",1)])),_:1})])),_:1}),"processing"===i.progressData.status?(l(),n(h,{key:0,class:"progress-details"},{default:r((()=>[d(m,{class:"detail-text"},{default:r((()=>[u(p(a.getProgressDetails()),1)])),_:1})])),_:1})):f("",!0)])),_:1}),i.progressData.originalSize?(l(),n(h,{key:0,class:"file-info"},{default:r((()=>[d(h,{class:"info-row"},{default:r((()=>[d(m,{class:"info-label"},{default:r((()=>[u("原始大小:")])),_:1}),d(m,{class:"info-value"},{default:r((()=>[u(p(a.formatFileSize(i.progressData.originalSize)),1)])),_:1})])),_:1}),i.progressData.compressedSize?(l(),n(h,{key:0,class:"info-row"},{default:r((()=>[d(m,{class:"info-label"},{default:r((()=>[u("压缩后:")])),_:1}),d(m,{class:"info-value"},{default:r((()=>[u(p(a.formatFileSize(i.progressData.compressedSize)),1)])),_:1})])),_:1})):f("",!0),i.progressData.compressionRatio?(l(),n(h,{key:1,class:"info-row"},{default:r((()=>[d(m,{class:"info-label"},{default:r((()=>[u("压缩率:")])),_:1}),d(m,{class:"info-value compression-ratio"},{default:r((()=>[u(p(i.progressData.compressionRatio)+"%",1)])),_:1})])),_:1})):f("",!0)])),_:1})):f("",!0),d(h,{class:"status-section"},{default:r((()=>["waiting"===i.progressData.status?(l(),n(h,{key:0,class:"status-item"},{default:r((()=>[d(m,{class:"status-icon"},{default:r((()=>[u("⏳")])),_:1}),d(m,{class:"status-text"},{default:r((()=>[u("等待压缩开始...")])),_:1})])),_:1})):"processing"===i.progressData.status?(l(),n(h,{key:1,class:"status-item"},{default:r((()=>[d(m,{class:"status-icon"},{default:r((()=>[u("⚡")])),_:1}),d(m,{class:"status-text"},{default:r((()=>[u("正在使用FFmpeg压缩视频，请稍候...")])),_:1})])),_:1})):"completed"===i.progressData.status?(l(),n(h,{key:2,class:"status-item success"},{default:r((()=>[d(m,{class:"status-icon"},{default:r((()=>[u("✅")])),_:1}),d(m,{class:"status-text"},{default:r((()=>[u("压缩完成！文件大小减少了"+p(Math.round(100*(1-i.progressData.compressionRatio/100)))+"%",1)])),_:1})])),_:1})):"failed"===i.progressData.status?(l(),n(h,{key:3,class:"status-item error"},{default:r((()=>[d(m,{class:"status-icon"},{default:r((()=>[u("❌")])),_:1}),d(m,{class:"status-text"},{default:r((()=>[u("压缩失败: "+p(i.progressData.errorMessage||"未知错误"),1)])),_:1})])),_:1})):f("",!0)])),_:1}),d(h,{class:"action-buttons"},{default:r((()=>[i.canClose?(l(),n(g,{key:0,class:"btn btn-secondary",onClick:a.closeProgress},{default:r((()=>[u(p("completed"===i.progressData.status?"完成":"后台运行"),1)])),_:1},8,["onClick"])):f("",!0),"failed"===i.progressData.status?(l(),n(g,{key:1,class:"btn btn-primary",onClick:a.retryCompression},{default:r((()=>[u(" 重试 ")])),_:1},8,["onClick"])):f("",!0)])),_:1})])),_:1})])),_:1})):f("",!0)}],["__scopeId","data-v-e096fb35"]]);const j=new class{constructor(){this.uploadProgress=0,this.isUploading=!1}async getFileFromPath(e,t=null){return new Promise(((s,o)=>{let i=t;if(i||(i=e.startsWith("blob:")?"file":e.split("/").pop()||e.split("\\").pop()||"file"),e.startsWith("blob:"))fetch(e).then((e=>e.blob())).then((e=>{let t=this.getExtensionFromMimeType(e.type);"file"!==i&&i.includes(".")||(i+=t),console.log(`创建File对象: 文件名=${i}, 类型=${e.type}, 大小=${e.size}`);const o=new File([e],i,{type:e.type});s(o)})).catch(o);else{uni.getFileSystemManager().readFile({filePath:e,success:t=>{const o=i.toLowerCase().split(".").pop();let a=this.getMimeTypeFromExtension(o);console.log(`创建File对象: 文件名=${i}, 类型=${a}, 路径=${e}`);const l=new Blob([t.data],{type:a}),n=new File([l],i,{type:a});s(n)},fail:o})}}))}getExtensionFromMimeType(e){return e.startsWith("video/")?e.includes("mp4")?".mp4":e.includes("avi")?".avi":e.includes("mov")?".mov":e.includes("mkv")?".mkv":e.includes("wmv")?".wmv":e.includes("flv")?".flv":e.includes("webm")?".webm":".mp4":e.startsWith("image/")?e.includes("jpeg")||e.includes("jpg")?".jpg":e.includes("png")?".png":e.includes("gif")?".gif":e.includes("webp")?".webp":".jpg":""}getMimeTypeFromExtension(e){return{mp4:"video/mp4",avi:"video/avi",mov:"video/mov",mkv:"video/mkv",wmv:"video/wmv",flv:"video/flv",webm:"video/webm",jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png",gif:"image/gif",webp:"image/webp"}[e]||"application/octet-stream"}buildQuestionsForApi(e){console.log("构建问题数据，原始数据:",e);const t=e.map(((e,t)=>{if(!e.content||!e.options)throw console.error("问题数据不完整:",e),new Error(`问题${t+1}数据不完整`);return{questionText:e.content.trim(),orderNum:t,options:e.options.map(((t,s)=>({optionText:t.text.trim(),isCorrect:s===e.correctAnswer,orderNum:s})))}}));return console.log("构建后的问题数据:",t),t}getAuthToken(){const e=x.getLoginInfo();return e&&e.accessToken?`Bearer ${e.accessToken}`:""}async uploadVideoAndCover(e,t=null){return new Promise((async(s,o)=>{try{console.log("开始准备FormData上传..."),console.log("视频文件路径:",e.path),console.log("封面文件路径:",e.thumbnail);const i=new FormData,a=await this.getFileFromPath(e.path,"video.mp4");i.append("VideoFile",a),console.log("已添加视频文件到FormData，文件名:",a.name,"类型:",a.type);const l=await this.getFileFromPath(e.thumbnail,"cover.jpg");i.append("CoverFile",l),console.log("已添加封面文件到FormData，文件名:",l.name,"类型:",l.type);const n=e.title.trim(),r=e.description||"",d=parseFloat(e.totalReward).toString(),c=JSON.stringify(this.buildQuestionsForApi(e.questions));i.append("Title",n),i.append("Description",r),i.append("RewardAmount",d),i.append("QuestionsJson",c);const u=!1!==e.enableCompression,p=e.compressionQuality||7;i.append("EnableCompression",u.toString()),i.append("CompressionQuality",p.toString()),console.log("FormData准备完成，数据详情:"),console.log("Title:",n),console.log("Description:",r),console.log("RewardAmount:",d),console.log("QuestionsJson:",c),console.log("开始上传...");const f=new XMLHttpRequest;f.upload.addEventListener("progress",(e=>{e.lengthComputable&&(this.uploadProgress=Math.round(e.loaded/e.total*100),console.log("上传进度:",this.uploadProgress+"%"),t&&t(this.uploadProgress))})),f.addEventListener("load",(()=>{if(console.log("上传完成，状态码:",f.status),console.log("响应数据:",f.responseText),200===f.status)try{const e=JSON.parse(f.responseText);console.log("解析后的响应:",e),e.success&&e.data?(this.uploadProgress=100,s(e.data)):o(new Error(e.msg||e.message||"上传失败"))}catch(e){console.error("响应解析失败:",e),o(new Error(`服务器返回了无效的数据格式: ${f.responseText}`))}else o(new Error(`服务器返回错误状态码: ${f.status}`))})),f.addEventListener("error",(()=>{console.error("上传请求失败"),o(new Error("网络请求失败"))})),f.open("POST",S()+"/Video/upload-complete"),f.setRequestHeader("Authorization",this.getAuthToken()),f.send(i)}catch(i){console.error("FormData准备失败:",i),o(i)}}))}};const O=new class{validateBasicInfo(e,t=!1){return t||e.path?e.title&&e.title.trim()?e.title.trim().length<2?{valid:!1,message:"视频标题至少需要2个字符"}:e.title.trim().length>100?{valid:!1,message:"视频标题不能超过100个字符"}:e.thumbnail?e.description&&e.description.length>500?{valid:!1,message:"视频描述不能超过500个字符"}:{valid:!0,message:""}:{valid:!1,message:"请选择封面图片"}:{valid:!1,message:"请输入视频标题"}:{valid:!1,message:"请选择视频文件"}}validateQuestions(e){if(!e||0===e.length)return{valid:!1,message:"至少需要添加一个问题"};for(let t=0;t<e.length;t++){const s=e[t];if(!s.content||!s.content.trim())return{valid:!1,message:`问题${t+1}：请输入问题内容`};if(s.content.trim().length<5)return{valid:!1,message:`问题${t+1}：问题内容至少需要5个字符`};if(s.content.trim().length>200)return{valid:!1,message:`问题${t+1}：问题内容不能超过200个字符`};if(!s.options||s.options.length<2)return{valid:!1,message:`问题${t+1}：至少需要2个选项`};if(s.options.length>6)return{valid:!1,message:`问题${t+1}：选项不能超过6个`};for(let e=0;e<s.options.length;e++){const o=s.options[e];if(!o.text||!o.text.trim())return{valid:!1,message:`问题${t+1}选项${e+1}：请输入选项内容`};if(o.text.trim().length>100)return{valid:!1,message:`问题${t+1}选项${e+1}：选项内容不能超过100个字符`}}if(void 0===s.correctAnswer||null===s.correctAnswer||s.correctAnswer>=s.options.length||s.correctAnswer<0)return{valid:!1,message:`问题${t+1}：请选择正确答案`}}return{valid:!0,message:""}}validateReward(e){if(!e)return{valid:!1,message:"请输入红包金额"};const t=parseFloat(e);return isNaN(t)||t<=0?{valid:!1,message:"请输入有效的红包金额"}:t<.01?{valid:!1,message:"红包金额不能少于0.01元"}:t>999999.99?{valid:!1,message:"红包金额不能超过999999.99元"}:{valid:!0,message:""}}validateVideoForm(e,t=!1){const s=this.validateBasicInfo(e,t);if(!s.valid)return s;const o=this.validateQuestions(e.questions);if(!o.valid)return o;const i=this.validateReward(e.totalReward);return i.valid?{valid:!0,message:"验证通过"}:i}};const G=e({mixins:[M],components:{VideoUploader:B,CompressionProgress:N},data:()=>({steps:["视频信息","添加问题"],currentStep:0,videoInfo:{path:"",thumbnail:"",duration:"",title:"",description:"",questions:[{content:"",options:[{text:""},{text:""},{text:""}],correctAnswer:0}],totalReward:"1.00",fileSize:0,resolution:"",format:"",bitrate:"",fps:""},isUploading:!1,uploadProgress:0,isEditMode:!1,videoId:null,originalVideoData:null,showCompressionProgress:!1,compressionFileId:""}),computed:{canGoNext(){if(0===this.currentStep)return!(!this.isEditMode&&!this.videoInfo.path)&&(!(!this.videoInfo.title||!this.videoInfo.title.trim())&&!(!this.isEditMode&&!this.videoInfo.thumbnail));if(1===this.currentStep){if(!this.videoInfo.totalReward||parseFloat(this.videoInfo.totalReward)<=0)return!1;if(!this.videoInfo.questions||0===this.videoInfo.questions.length)return!1;for(let e of this.videoInfo.questions){if(!e.content||!e.content.trim())return!1;if(!e.options||e.options.length<2)return!1;for(let t of e.options)if(!t.text||!t.text.trim())return!1}return!0}return!0}},onLoad(e){const{id:t}=e??{};t?(this.isEditMode=!0,this.videoId=parseInt(t),this.loadVideoData()):this.currentStep=0},onShow(){this.isEditMode||(this.currentStep=0)},methods:{onVideoInfoChange(e){this.videoInfo={...e}},async loadVideoData(){try{const e=await R((()=>z(this.videoId)),"加载视频信息...","加载视频信息失败");if(!e.success||!e.data)throw new Error(e.msg||"获取视频详情失败");{const t=e.data;this.originalVideoData=t,this.videoInfo={path:t.videoUrl??"",thumbnail:t.coverUrl??"/assets/images/video-cover.jpg",duration:this.formatDuration(t.duration??0),title:t.title??"",description:t.description??"",questions:this.processQuestionsForEdit(t.questions??[]),totalReward:(t.rewardAmount??0).toString()}}}catch(e){Q("加载视频信息失败")}},processQuestionsForEdit:e=>e&&0!==e.length?e.map((e=>{const t=(e.options||[]).map((e=>({text:e.optionText||e.text||""})));for(;t.length<3;)t.push({text:""});let s=0;if(e.options&&e.options.length>0){const t=e.options.find((e=>e.isCorrect));t&&(s=e.options.indexOf(t))}return{content:e.questionText||e.question||"",options:t,correctAnswer:s}})):[{content:"",options:[{text:""},{text:""},{text:""}],correctAnswer:0}],addQuestion(){this.videoInfo.questions.push({content:"",options:[{text:""},{text:""},{text:""}],correctAnswer:0})},removeQuestion(e){D({title:"删除确认",content:"确定要删除这个问题吗？",success:t=>{t.confirm&&this.videoInfo.questions.splice(e,1)}})},setCorrectAnswer(e,t){this.videoInfo.questions[e].correctAnswer=t},addOption(e){this.videoInfo.questions[e].options.push({text:""})},removeOption(e,s){const o=this.videoInfo.questions[e];o.options.length<=2?t({title:"至少需要2个选项",icon:"none"}):(o.correctAnswer===s?o.correctAnswer=0:o.correctAnswer>s&&o.correctAnswer--,o.options.splice(s,1))},nextStep(){if(this.canGoNext){if(0===this.currentStep){if(!this.isEditMode&&!this.videoInfo.path)return void t({title:"请选择视频文件",icon:"none"});if(!this.videoInfo.title||!this.videoInfo.title.trim())return void t({title:"请输入视频标题",icon:"none"});if(!this.isEditMode&&!this.videoInfo.thumbnail)return void t({title:"请选择视频封面",icon:"none"})}else if(1===this.currentStep){if(!this.videoInfo.totalReward||parseFloat(this.videoInfo.totalReward)<=0)return void t({title:"请输入有效的红包金额",icon:"none"});if(0===this.videoInfo.questions.length)return void t({title:"请至少添加一个问题",icon:"none"});for(let e=0;e<this.videoInfo.questions.length;e++){const s=this.videoInfo.questions[e];if(!s.content||!s.content.trim())return void t({title:`问题${e+1}：请输入问题内容`,icon:"none"});if(s.options.length<2)return void t({title:`问题${e+1}：至少需要2个选项`,icon:"none"});for(let o=0;o<s.options.length;o++)if(!s.options[o].text||!s.options[o].text.trim())return void t({title:`问题${e+1}：请输入选项${String.fromCharCode(65+o)}内容`,icon:"none"})}}this.currentStep<1&&this.currentStep++}},prevStep(){this.currentStep>0&&this.currentStep--},validateForm(){const e=O.validateVideoForm(this.videoInfo,this.isEditMode);return!!e.valid||(this.showError(e.message),!1)},submitUpload(){this.validateForm()&&(o({title:this.isEditMode?"保存中...":"上传中...",mask:!0}),this.isEditMode?(i(),this.updateVideo()):this.createVideo())},async createVideo(){try{if(console.log("Creating video with data:",this.videoInfo),o({title:"准备上传...",mask:!0}),!this.videoInfo.path)throw new Error("请先选择视频文件");if(!this.videoInfo.thumbnail)throw new Error("请选择视频封面图片");if(!this.videoInfo.title||!this.videoInfo.title.trim())throw new Error("请输入视频标题");const e=parseFloat(this.videoInfo.totalReward);if(!this.videoInfo.totalReward||isNaN(e)||e<=0)throw new Error("请输入有效的红包金额");if(!this.videoInfo.questions||0===this.videoInfo.questions.length)throw new Error("请至少添加一个问题");for(let t=0;t<this.videoInfo.questions.length;t++){const e=this.videoInfo.questions[t];if(!e.content||!e.content.trim())throw new Error(`问题${t+1}：请输入问题内容`);if(!e.options||e.options.length<2)throw new Error(`问题${t+1}：至少需要2个选项`);for(let s=0;s<e.options.length;s++)if(!e.options[s].text||!e.options[s].text.trim())throw new Error(`问题${t+1}：请填写选项${String.fromCharCode(65+s)}`)}this.isUploading=!0,this.uploadProgress=0,i(),console.log("=== 上传数据详情 ==="),console.log("视频路径:",this.videoInfo.path),console.log("封面路径:",this.videoInfo.thumbnail),console.log("标题:",this.videoInfo.title),console.log("描述:",this.videoInfo.description),console.log("红包金额:",this.videoInfo.totalReward,"类型:",typeof this.videoInfo.totalReward),console.log("问题数据:",JSON.stringify(this.videoInfo.questions,null,2)),console.log("=================="),console.log("开始同时上传视频和封面..."),await this.performUpload()}catch(e){console.error("视频上传失败:",e),i(),this.isUploading=!1,this.uploadProgress=0,this.showDetailedError("上传失败",e.message||"未知错误，请重试")}},isCustomCover:e=>!!e&&(e.startsWith("blob:")||e.startsWith("file://")||e.includes("temp")||e.includes("tmp")),async performUpload(){try{const e=await j.uploadVideoAndCover(this.videoInfo,(e=>{this.uploadProgress=e}));return this.videoInfo.enableCompression&&e.fileId?(this.compressionFileId=e.fileId,this.showCompressionProgress=!0,this.showSuccess("上传成功，开始压缩")):(this.showSuccess("上传成功"),setTimeout((()=>{this.showUploadSuccessOptions()}),2e3)),e}catch(e){throw console.error("上传失败:",e),e}},async updateVideo(){var e,s,o,i;try{const a={title:this.videoInfo.title,description:this.videoInfo.description,coverUrl:(null==(e=this.originalVideoData)?void 0:e.coverUrl)||this.videoInfo.thumbnail||"",videoUrl:(null==(s=this.originalVideoData)?void 0:s.videoUrl)||this.videoInfo.path||"",duration:(null==(o=this.originalVideoData)?void 0:o.duration)||this.parseDurationToSeconds(this.videoInfo.duration)||0,rewardAmount:parseFloat(this.videoInfo.totalReward)||0,viewCount:(null==(i=this.originalVideoData)?void 0:i.viewCount)||0,questions:j.buildQuestionsForApi(this.videoInfo.questions)};console.log("更新视频数据:",JSON.stringify(a,null,2));const l=await U(this.videoId,a);if(!l.success)throw new Error(l.msg||"保存失败");t({title:"保存成功",icon:"success"}),setTimeout((()=>{F()}),1500)}catch(a){console.error("更新视频失败:",a),t({title:a.message||"保存失败",icon:"none"})}},parseDurationToSeconds(e){if(!e)return 0;const t=e.split(":");return 2===t.length?60*parseInt(t[0])+parseInt(t[1]):0},async getCoverFileFromPath(e){try{return e?{path:e,name:`cover_${Date.now()}.jpg`,type:"image/jpeg"}:null}catch(t){return console.error("获取封面文件失败:",t),null}},showDetailedError(e,t){console.error(`${e}:`,t),this.isUploading=!1,this.uploadProgress=0,this.showError(t)},async goBack(){await this.showConfirm("提示",this.isEditMode?"确定要放弃当前修改并返回吗？":"确定要放弃当前编辑并返回吗？")&&this.safeNavigateBack()},showUploadSuccessOptions(){this.isUploading=!1,this.uploadProgress=0,D({title:"上传成功",content:"视频上传成功！您希望继续上传新视频还是返回视频列表？",confirmText:"返回列表",cancelText:"继续上传",success:e=>{e.confirm?this.goBackToListWithRefresh():this.resetFormForNewUpload()}})},goBackToListWithRefresh(){P("refreshVideoList"),V({url:"/pages/admin/media/index",fail:e=>{console.error("重新启动失败:",e),this.safeNavigateBack()}})},resetFormForNewUpload(){this.videoInfo={title:"",description:"",rewardAmount:.01,questions:[{content:"",options:[{text:""},{text:""},{text:""}],correctAnswer:0}],fileSize:0,duration:"",format:"",resolution:""},this.currentStep=0,this.isUploading=!1,this.uploadProgress=0,t({title:"已重置，可以上传新视频",icon:"success"})},onCompressionProgressClose(){this.showCompressionProgress=!1,setTimeout((()=>{this.showUploadSuccessOptions()}),500)},onCompressionRetry(e){console.log("重试压缩:",e),t({title:"重新开始压缩",icon:"success"})}}},[["render",function(e,t,s,o,i,a){const h=w,y=E("VideoUploader"),b=_,C=T,x=q,S=I,D=A,F=E("CompressionProgress");return l(),n(h,{class:"container"},{default:r((()=>[d(h,{class:"form-container"},{default:r((()=>[d(h,{class:"steps-container"},{default:r((()=>[d(h,{class:"steps"},{default:r((()=>[(l(!0),m(v,null,g(i.steps,((e,t)=>(l(),n(h,{key:t,class:c(["step-item",i.currentStep>=t?"active":""])},{default:r((()=>[d(h,{class:"step-number"},{default:r((()=>[u(p(t+1),1)])),_:2},1024),d(h,{class:"step-title"},{default:r((()=>[u(p(e),1)])),_:2},1024),t<i.steps.length-1?(l(),n(h,{key:0,class:"step-line"})):f("",!0)])),_:2},1032,["class"])))),128))])),_:1})])),_:1}),0===i.currentStep?(l(),n(h,{key:0,class:"step-content"},{default:r((()=>[d(h,{class:"content-section"},{default:r((()=>[d(y,{value:i.videoInfo,"is-edit-mode":i.isEditMode,onInput:a.onVideoInfoChange},null,8,["value","is-edit-mode","onInput"]),d(h,{class:"form-group"},{default:r((()=>[d(h,{class:"form-item"},{default:r((()=>[d(b,{class:"form-label required"},{default:r((()=>[u("视频标题")])),_:1}),d(C,{type:"text",class:"modern-input",placeholder:"请输入视频标题",modelValue:i.videoInfo.title,"onUpdate:modelValue":t[0]||(t[0]=e=>i.videoInfo.title=e)},null,8,["modelValue"])])),_:1}),d(h,{class:"form-item"},{default:r((()=>[d(b,{class:"form-label"},{default:r((()=>[u("视频描述")])),_:1}),d(x,{class:"modern-textarea",placeholder:"请输入视频描述（可选）",modelValue:i.videoInfo.description,"onUpdate:modelValue":t[1]||(t[1]=e=>i.videoInfo.description=e)},null,8,["modelValue"])])),_:1})])),_:1})])),_:1}),d(h,{class:"form-actions"},{default:r((()=>[d(S,{class:c(["btn btn-primary",{"btn-disabled":!a.canGoNext}]),onClick:a.nextStep,disabled:!a.canGoNext},{default:r((()=>[u(" 下一步 ")])),_:1},8,["class","onClick","disabled"])])),_:1})])),_:1})):f("",!0),1===i.currentStep?(l(),n(h,{key:1,class:"step-content"},{default:r((()=>[d(h,{class:"content-section"},{default:r((()=>[d(h,{class:"question-actions"},{default:r((()=>[d(S,{class:"add-question-btn",onClick:a.addQuestion},{default:r((()=>[d(b,{class:"btn-icon"},{default:r((()=>[u("+")])),_:1}),d(b,{class:"btn-text"},{default:r((()=>[u("添加问题")])),_:1})])),_:1},8,["onClick"])])),_:1}),0===i.videoInfo.questions.length?(l(),n(h,{key:0,class:"empty-list"},{default:r((()=>[d(b,{class:"empty-icon iconfont icon-empty"}),d(b,{class:"empty-text"},{default:r((()=>[u('暂无问题，请点击"添加问题"按钮添加')])),_:1})])),_:1})):(l(),n(h,{key:1,class:"question-list"},{default:r((()=>[(l(!0),m(v,null,g(i.videoInfo.questions,((e,t)=>(l(),n(h,{class:"question-item",key:t},{default:r((()=>[d(h,{class:"question-header"},{default:r((()=>[d(b,{class:"question-number"},{default:r((()=>[u("问题 "+p(t+1),1)])),_:2},1024),d(b,{class:"question-delete",onClick:e=>a.removeQuestion(t)},{default:r((()=>[u("删除")])),_:2},1032,["onClick"])])),_:2},1024),d(h,{class:"form-item"},{default:r((()=>[d(b,{class:"form-label required"},{default:r((()=>[u("问题内容")])),_:1}),d(C,{type:"text",class:"form-input",placeholder:"请输入问题内容",modelValue:e.content,"onUpdate:modelValue":t=>e.content=t},null,8,["modelValue","onUpdate:modelValue"])])),_:2},1024),d(h,{class:"form-item"},{default:r((()=>[d(h,{class:"option-header"},{default:r((()=>[d(b,{class:"form-label required"},{default:r((()=>[u("选项")])),_:1}),d(b,{class:"option-action",onClick:e=>a.addOption(t)},{default:r((()=>[u("+ 添加选项")])),_:2},1032,["onClick"])])),_:2},1024),d(h,{class:"option-list"},{default:r((()=>[(l(!0),m(v,null,g(e.options,((s,o)=>(l(),n(h,{class:"option-item",key:o},{default:r((()=>[d(b,{class:"option-marker"},{default:r((()=>[u(p(String.fromCharCode(65+o)),1)])),_:2},1024),d(C,{type:"text",class:"option-input",placeholder:"请输入选项内容",modelValue:s.text,"onUpdate:modelValue":e=>s.text=e},null,8,["modelValue","onUpdate:modelValue"]),d(D,{checked:e.correctAnswer===o,onClick:e=>a.setCorrectAnswer(t,o)},null,8,["checked","onClick"]),e.options.length>2?(l(),n(b,{key:0,class:"option-delete",onClick:e=>a.removeOption(t,o)},{default:r((()=>[u("删除")])),_:2},1032,["onClick"])):f("",!0)])),_:2},1024)))),128))])),_:2},1024)])),_:2},1024)])),_:2},1024)))),128))])),_:1})),d(h,{class:"reward-section"},{default:r((()=>[d(h,{class:"reward-input-group"},{default:r((()=>[d(h,{class:"form-item"},{default:r((()=>[d(b,{class:"form-label required"},{default:r((()=>[u("红包金额（元）")])),_:1}),d(C,{type:"digit",class:"modern-input reward-input",placeholder:"请输入红包金额",modelValue:i.videoInfo.totalReward,"onUpdate:modelValue":t[2]||(t[2]=e=>i.videoInfo.totalReward=e)},null,8,["modelValue"]),d(b,{class:"form-hint"},{default:r((()=>[u("用户答对问题后可获得的红包金额")])),_:1})])),_:1})])),_:1})])),_:1})])),_:1}),d(h,{class:"form-actions"},{default:r((()=>[d(S,{class:"btn btn-secondary",onClick:a.prevStep},{default:r((()=>[u("上一步")])),_:1},8,["onClick"]),d(S,{class:c(["btn btn-primary",{"btn-disabled":i.isUploading||!a.canGoNext}]),onClick:a.submitUpload,disabled:i.isUploading||!a.canGoNext},{default:r((()=>[u(p(i.isUploading?"上传中...":i.isEditMode?"保存修改":"完成上传"),1)])),_:1},8,["class","onClick","disabled"])])),_:1})])),_:1})):f("",!0)])),_:1}),i.isUploading?(l(),n(h,{key:0,class:"upload-progress-container"},{default:r((()=>[d(h,{class:"progress-mask"}),d(h,{class:"progress-card"},{default:r((()=>[d(h,{class:"progress-header"},{default:r((()=>[d(b,{class:"progress-title"},{default:r((()=>[u("正在上传视频")])),_:1}),d(b,{class:"progress-percent"},{default:r((()=>[u(p(i.uploadProgress)+"%",1)])),_:1})])),_:1}),d(h,{class:"progress-bar"},{default:r((()=>[d(h,{class:"progress-fill",style:k({width:i.uploadProgress+"%"})},null,8,["style"])])),_:1}),d(h,{class:"progress-info"},{default:r((()=>[d(b,{class:"progress-text"},{default:r((()=>[u("请耐心等待，上传完成后会自动提示")])),_:1}),d(b,{class:"progress-warning"},{default:r((()=>[u("⚠️ 请勿离开页面或锁屏，以免上传中断")])),_:1}),d(b,{class:"progress-tip"},{default:r((()=>[u("💡 建议保持网络连接稳定")])),_:1})])),_:1})])),_:1})])),_:1})):f("",!0),d(F,{visible:i.showCompressionProgress,fileId:i.compressionFileId,onClose:a.onCompressionProgressClose,onRetry:a.onCompressionRetry},null,8,["visible","fileId","onClose","onRetry"])])),_:1})}],["__scopeId","data-v-5f5db32f"]]);export{G as default};

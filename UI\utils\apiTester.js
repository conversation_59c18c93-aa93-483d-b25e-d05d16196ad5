/**
 * API连接测试工具
 * 用于测试前端与后端API的连接状态
 */

import { getApiBaseURL } from './config-manager.js'
import { getWechatAuthorizeUrl, checkUserTokenStatus } from '../api/wechat.js'
import { getBatchDetail } from '../api/batch.js'

/**
 * API测试器
 */
class ApiTester {
  /**
   * 测试基础连接
   * @returns {Promise<Object>} 连接测试结果
   */
  async testBasicConnection() {
    try {
      const baseUrl = getApiBaseURL()
      const testUrl = `${baseUrl}/health` // 假设有健康检查接口
      
      const response = await uni.request({
        url: testUrl,
        method: 'GET',
        timeout: 5000
      })

      return {
        success: response.statusCode === 200,
        statusCode: response.statusCode,
        baseUrl: baseUrl,
        message: response.statusCode === 200 ? '连接正常' : `HTTP ${response.statusCode}`
      }
    } catch (error) {
      return {
        success: false,
        baseUrl: getApiBaseURL(),
        error: error.message || '连接失败',
        message: '无法连接到后端服务器'
      }
    }
  }

  /**
   * 测试微信授权URL接口
   * @returns {Promise<Object>} 测试结果
   */
  async testWechatAuthUrl() {
    try {
      const response = await getWechatAuthorizeUrl({
        redirectUri: 'http://test.com/callback',
        state: 'test_state'
      })

      return {
        success: response.success,
        hasAuthUrl: !!(response.data && response.data.authUrl),
        response: response,
        message: response.success ? '微信授权URL接口正常' : (response.message || '接口调用失败')
      }
    } catch (error) {
      return {
        success: false,
        error: error.message || '接口调用异常',
        message: '微信授权URL接口测试失败'
      }
    }
  }

  /**
   * 测试Token状态检查接口
   * @returns {Promise<Object>} 测试结果
   */
  async testTokenStatus() {
    try {
      const response = await checkUserTokenStatus()

      return {
        success: true, // 能调用就算成功，即使Token无效
        response: response,
        message: 'Token状态检查接口正常'
      }
    } catch (error) {
      return {
        success: false,
        error: error.message || '接口调用异常',
        message: 'Token状态检查接口测试失败'
      }
    }
  }

  /**
   * 测试批次详情接口
   * @param {number} batchId 批次ID
   * @returns {Promise<Object>} 测试结果
   */
  async testBatchDetail(batchId = 1) {
    try {
      const response = await getBatchDetail(batchId)

      return {
        success: response.success,
        hasBatchData: !!(response.data),
        response: response,
        message: response.success ? '批次详情接口正常' : (response.message || '接口调用失败')
      }
    } catch (error) {
      return {
        success: false,
        error: error.message || '接口调用异常',
        message: '批次详情接口测试失败'
      }
    }
  }

  /**
   * 运行所有API测试
   * @param {Object} options 测试选项
   * @returns {Promise<Object>} 完整测试结果
   */
  async runAllTests(options = {}) {
    const results = {
      timestamp: new Date().toISOString(),
      baseUrl: getApiBaseURL(),
      tests: {}
    }

    console.log('🧪 开始API连接测试...')

    // 测试基础连接
    console.log('📡 测试基础连接...')
    results.tests.basicConnection = await this.testBasicConnection()

    // 测试微信授权URL接口
    console.log('🔐 测试微信授权URL接口...')
    results.tests.wechatAuthUrl = await this.testWechatAuthUrl()

    // 测试Token状态检查接口
    console.log('🔑 测试Token状态检查接口...')
    results.tests.tokenStatus = await this.testTokenStatus()

    // 测试批次详情接口
    if (options.batchId) {
      console.log(`📹 测试批次详情接口 (ID: ${options.batchId})...`)
      results.tests.batchDetail = await this.testBatchDetail(options.batchId)
    }

    // 生成总结
    results.summary = this.generateTestSummary(results.tests)

    console.log('✅ API测试完成')
    return results
  }

  /**
   * 生成测试总结
   * @param {Object} tests 测试结果
   * @returns {Object} 测试总结
   */
  generateTestSummary(tests) {
    const testNames = Object.keys(tests)
    const passedTests = testNames.filter(name => tests[name].success)
    const failedTests = testNames.filter(name => !tests[name].success)

    return {
      total: testNames.length,
      passed: passedTests.length,
      failed: failedTests.length,
      passRate: testNames.length > 0 ? (passedTests.length / testNames.length * 100).toFixed(1) : 0,
      passedTests,
      failedTests,
      overallStatus: failedTests.length === 0 ? 'PASS' : 'FAIL'
    }
  }

  /**
   * 打印测试结果
   * @param {Object} results 测试结果
   */
  printTestResults(results) {
    console.group('🧪 API连接测试结果')
    console.log('🌐 API基础地址:', results.baseUrl)
    console.log('📊 测试总结:', results.summary)
    
    Object.entries(results.tests).forEach(([testName, result]) => {
      const icon = result.success ? '✅' : '❌'
      console.log(`${icon} ${testName}:`, result.message)
      if (!result.success && result.error) {
        console.log(`   错误: ${result.error}`)
      }
    })
    
    console.groupEnd()
  }

  /**
   * 生成问题诊断建议
   * @param {Object} results 测试结果
   * @returns {Array<string>} 建议列表
   */
  generateRecommendations(results) {
    const recommendations = []
    const { tests } = results

    // 基础连接问题
    if (!tests.basicConnection?.success) {
      recommendations.push('后端服务器连接失败，请检查：')
      recommendations.push('  - 后端服务是否正常运行')
      recommendations.push('  - API基础地址配置是否正确')
      recommendations.push('  - 网络连接是否正常')
      recommendations.push('  - 防火墙或代理设置')
    }

    // 微信接口问题
    if (!tests.wechatAuthUrl?.success) {
      recommendations.push('微信授权接口异常，请检查：')
      recommendations.push('  - 后端微信配置是否正确')
      recommendations.push('  - 微信AppId和AppSecret是否有效')
      recommendations.push('  - 接口路径是否正确 (/api/Wechat/auth-url)')
    }

    // Token接口问题
    if (!tests.tokenStatus?.success) {
      recommendations.push('Token状态检查接口异常，请检查：')
      recommendations.push('  - 接口路径是否正确 (/api/Wechat/token-status)')
      recommendations.push('  - 后端认证中间件配置')
    }

    // 批次接口问题
    if (tests.batchDetail && !tests.batchDetail.success) {
      recommendations.push('批次详情接口异常，请检查：')
      recommendations.push('  - 批次数据是否存在')
      recommendations.push('  - 数据库连接是否正常')
      recommendations.push('  - 接口权限配置')
    }

    if (recommendations.length === 0) {
      recommendations.push('所有API接口测试通过，连接正常！')
    }

    return recommendations
  }
}

// 创建单例实例
const apiTester = new ApiTester()

// 导出测试器实例和类
export default apiTester
export { ApiTester }

// 导出常用测试方法
export const testApiConnection = (options) => apiTester.runAllTests(options)
export const testBasicConnection = () => apiTester.testBasicConnection()
export const testWechatAuthUrl = () => apiTester.testWechatAuthUrl()

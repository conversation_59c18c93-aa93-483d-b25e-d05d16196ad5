/**
 * 微信登录测试工具
 * 专门用于H5服务号环境的微信登录测试
 */

import wechatUserService from '@/utils/wechatUserService.js'
import wechatApi from '@/utils/wechatRequest.js'

class WechatTestTool {
  constructor() {
    this.logs = []
    this.isRunning = false
  }

  /**
   * 添加日志
   */
  addLog(message, type = 'info') {
    const log = {
      time: new Date().toLocaleTimeString(),
      message: message,
      type: type
    }
    this.logs.push(log)
    console.log(`[${type.toUpperCase()}] ${message}`)
    return log
  }

  /**
   * 获取所有日志
   */
  getLogs() {
    return this.logs
  }

  /**
   * 清空日志
   */
  clearLogs() {
    this.logs = []
    this.addLog('日志已清空', 'info')
  }

  /**
   * 复制日志到剪贴板
   */
  copyLogs() {
    if (this.logs.length === 0) {
      uni.showToast({
        title: '暂无日志可复制',
        icon: 'none'
      })
      return
    }

    const logText = this.logs.map(log => `[${log.time}] ${log.message}`).join('\n')
    
    uni.setClipboardData({
      data: logText,
      success: () => {
        uni.showToast({
          title: '日志已复制到剪贴板',
          icon: 'success'
        })
        this.addLog('日志已复制到剪贴板', 'success')
      },
      fail: () => {
        uni.showToast({
          title: '复制失败',
          icon: 'error'
        })
        this.addLog('日志复制失败', 'error')
      }
    })
  }

  /**
   * 触发微信登录测试
   */
  async testWechatLogin(options = {}) {
    if (this.isRunning) return
    
    this.isRunning = true
    this.addLog('开始微信登录测试', 'info')
    
    try {
      uni.showLoading({
        title: '测试登录中...',
        mask: true
      })

      // 清除现有登录状态
      this.addLog('清除现有登录状态', 'info')
      if (typeof wechatUserService.clearUserInfo === 'function') {
        wechatUserService.clearUserInfo()
      }
      if (typeof wechatUserService.clearUserToken === 'function') {
        wechatUserService.clearUserToken()
      }
      
      // 构造登录参数
      const loginOptions = {
        employeeId: options.employeeId || null,
        batchId: options.batchId || null,
        sharerId: options.sharerId || null,
        returnUrl: options.returnUrl || '/pages/video/index'
      }

      this.addLog(`登录参数: ${JSON.stringify(loginOptions)}`, 'info')

      // 调用微信登录
      const result = await wechatUserService.tryWechatAutoLogin(loginOptions)

      uni.hideLoading()

      if (result.success) {
        this.addLog(`登录成功: ${result.data.userInfo.nickname} (ID: ${result.data.userInfo.id})`, 'success')
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        return { success: true, data: result.data }
      } else {
        throw new Error(result.message || '登录失败')
      }
    } catch (error) {
      uni.hideLoading()
      this.addLog(`登录失败: ${error.message}`, 'error')
      
      // 检查是否是审核状态错误
      if (error.message && error.message.includes('审核状态')) {
        this.addLog('检测到审核状态错误，用户需要审核', 'warning')
        return { success: false, needAudit: true, error: error.message }
      }
      
      uni.showToast({
        title: '登录失败',
        icon: 'error'
      })
      return { success: false, error: error.message }
    } finally {
      this.isRunning = false
    }
  }

  /**
   * 获取微信授权URL（H5环境）
   */
  getWechatAuthUrl(options = {}) {
    this.addLog('H5环境：生成微信授权URL', 'info')
    
    // 这里应该根据你的实际配置生成微信授权URL
    const appId = 'your_wechat_appid' // 替换为实际的AppID
    const redirectUri = encodeURIComponent(window.location.origin + '/pages/video/index')
    const state = encodeURIComponent(JSON.stringify({
      timestamp: Date.now(),
      batchId: options.batchId,
      sharerId: options.sharerId
    }))
    
    const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_userinfo&state=${state}#wechat_redirect`
    
    this.addLog(`授权URL: ${authUrl}`, 'info')
    
    // 复制到剪贴板
    uni.setClipboardData({
      data: authUrl,
      success: () => {
        this.addLog('授权URL已复制到剪贴板', 'success')
        uni.showToast({
          title: 'URL已复制',
          icon: 'success'
        })
      }
    })
    
    return authUrl
  }

  /**
   * 清除登录信息
   */
  clearLoginInfo() {
    try {
      this.addLog('开始清除登录信息', 'info')
      
      // 清除微信用户服务中的信息
      if (typeof wechatUserService.clearUserInfo === 'function') {
        wechatUserService.clearUserInfo()
        this.addLog('已清除微信用户信息', 'info')
      }
      
      if (typeof wechatUserService.clearUserToken === 'function') {
        wechatUserService.clearUserToken()
        this.addLog('已清除微信用户Token', 'info')
      }
      
      // 清除本地存储中的相关信息
      const storageKeys = ['wechatUserInfo', 'wechatUserToken', 'userToken', 'userInfo']
      storageKeys.forEach(key => {
        uni.removeStorageSync(key)
        this.addLog(`已清除本地存储: ${key}`, 'info')
      })
      
      this.addLog('登录信息清除完成', 'success')
      
      uni.showToast({
        title: '登录信息已清除',
        icon: 'success'
      })
      
      return true
    } catch (error) {
      this.addLog(`清除登录信息失败: ${error.message}`, 'error')
      uni.showToast({
        title: '清除失败',
        icon: 'error'
      })
      return false
    }
  }

  /**
   * 查看登录状态
   */
  getLoginStatus() {
    try {
      this.addLog('开始查看登录状态', 'info')
      
      const isLoggedIn = wechatUserService.isLoggedIn()
      const userInfo = wechatUserService.getUserInfo()
      const userToken = wechatUserService.getUserToken()
      const userId = wechatUserService.getUserId()
      
      const status = {
        isLoggedIn,
        userInfo,
        userId,
        tokenLength: userToken ? userToken.length : 0
      }
      
      // 记录详细状态到日志
      this.addLog(`登录状态: ${isLoggedIn ? '已登录' : '未登录'}`, 'info')
      
      if (userInfo) {
        this.addLog(`用户昵称: ${userInfo.nickname || 'N/A'}`, 'info')
        this.addLog(`用户ID: ${userId || 'N/A'}`, 'info')
        this.addLog(`OpenID: ${userInfo.openId || 'N/A'}`, 'info')
      } else {
        this.addLog('用户信息: 无', 'warning')
      }
      
      this.addLog(`Token长度: ${status.tokenLength}`, 'info')
      this.addLog('登录状态查看完成', 'success')
      
      return status
    } catch (error) {
      this.addLog(`获取登录状态失败: ${error.message}`, 'error')
      return null
    }
  }

  /**
   * 显示审核状态弹窗
   */
  showAuditModal(userInfo) {
    const auditUserInfo = userInfo || wechatUserService.getUserInfo() || {
      id: 'unknown',
      nickname: '微信用户',
      avatar: '/static/images/default-avatar.png',
      openId: 'unknown'
    }
    
    this.addLog('显示审核状态弹窗', 'warning')
    
    return {
      showModal: true,
      userInfo: auditUserInfo,
      message: '您的账号正在审核中，暂时无法使用视频功能。请联系管理员进行账号审核。'
    }
  }
}

// 创建单例实例
const wechatTestTool = new WechatTestTool()

export default wechatTestTool

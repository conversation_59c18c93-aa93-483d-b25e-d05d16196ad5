{"apis": {"wechat": {"authorize": {"method": "GET", "url": "/api/Wechat/authorize", "requestParams": {"query": {"redirectUri": "string (required)", "scope": "string (default: snsapi_userinfo)", "state": "string (optional)", "inviterId": "string (optional)"}}, "responseParams": {"success": "boolean", "data": {"authUrl": "string", "inviterId": "string"}, "message": "string"}}, "callback": {"method": "GET", "url": "/api/Wechat/callback", "requestParams": {"query": {"code": "string (required)", "state": "string (optional)"}}, "responseParams": {"success": "boolean", "data": {"accessToken": "string", "userToken": "string", "userTokenExpiryDays": "number", "userInfo": {"id": "string", "openId": "string", "unionId": "string", "nickname": "string", "avatar": "string", "employeeId": "string", "employeeName": "string", "lastLogin": "string", "createTime": "string", "updateTime": "string"}, "isNewUser": "boolean", "auditStatus": "number", "inviterId": "string"}, "message": "string"}}, "configCheck": {"method": "GET", "url": "/api/Wechat/config-check", "requestParams": {}, "responseParams": {"success": "boolean", "data": {"appId": "string", "appSecretConfigured": "boolean", "appSecretLength": "number"}, "message": "string"}}, "tokenRefresh": {"method": "POST", "url": "/api/Wechat/token/refresh", "requestParams": {}, "responseParams": {"success": "boolean", "message": "string"}}, "tokenStatus": {"method": "GET", "url": "/api/Wechat/token/status", "requestParams": {}, "responseParams": {"success": "boolean", "data": {"hasValidToken": "boolean", "accessToken": "string"}, "message": "string"}}, "verify": {"method": "GET", "url": "/api/Wechat/verify", "requestParams": {"query": {"signature": "string (required)", "timestamp": "string (required)", "nonce": "string (required)", "echostr": "string (required)"}}, "responseParams": "string (plain text)"}}, "userAudit": {"auditUser": {"method": "POST", "url": "/api/UserAudit/audit-user/{userId}", "headers": {"Authorization": "Bearer {accessToken}"}, "requestParams": {"path": {"userId": "string (required)"}, "body": {"status": "number (required, 1 or 2)", "remark": "string (optional, max 255 chars)"}}, "responseParams": {"success": "boolean", "data": "boolean", "message": "string"}}, "getPendingUsers": {"method": "GET", "url": "/api/UserAudit/pending-users", "headers": {"Authorization": "Bearer {accessToken}"}, "requestParams": {}, "responseParams": {"success": "boolean", "data": [{"id": "string", "openId": "string", "unionId": "string", "nickname": "string", "avatar": "string", "employeeId": "string", "employeeName": "string", "lastLogin": "string", "createTime": "string", "updateTime": "string"}], "message": "string"}}, "getAllPendingUsers": {"method": "GET", "url": "/api/UserAudit/all-pending-users", "headers": {"Authorization": "Bearer {accessToken}"}, "requestParams": {}, "responseParams": {"success": "boolean", "data": [{"id": "string", "openId": "string", "unionId": "string", "nickname": "string", "avatar": "string", "employeeId": "string", "employeeName": "string", "lastLogin": "string", "createTime": "string", "updateTime": "string"}], "message": "string"}}}, "userBatchRecord": {"createOrGet": {"method": "POST", "url": "/api/UserBatchRecord/create-or-get", "headers": {"Authorization": "Bearer {userToken} (optional for anonymous)"}, "requestParams": {"body": {"batchId": "number (required)", "userId": "string (optional)", "promotionLink": "string (optional, max 500 chars)"}}, "responseParams": {"success": "boolean", "data": {"id": "number", "userId": "string", "userNickname": "string", "userAvatar": "string", "employeeId": "string", "employeeName": "string", "batchId": "number", "batchName": "string", "videoId": "number", "videoTitle": "string", "videoCoverUrl": "string", "videoDuration": "number", "viewDuration": "number", "watchProgress": "number", "isCompleted": "boolean", "startTime": "string", "endTime": "string", "totalQuestions": "number", "correctAnswers": "number", "hasAnswered": "boolean", "correctRate": "number", "answerTime": "string", "rewardAmount": "number", "rewardStatus": "number", "rewardTime": "string", "promotionLink": "string", "createTime": "string", "updateTime": "string"}, "message": "string"}}, "updateWatchProgress": {"method": "POST", "url": "/api/UserBatchRecord/{userId}/watch-progress", "headers": {"Authorization": "Bearer {userToken}"}, "requestParams": {"path": {"userId": "string (required)"}, "body": {"batchId": "number (required)", "viewDuration": "number (required)", "watchProgress": "number (required)", "isCompleted": "boolean (optional)"}}, "responseParams": {"success": "boolean", "data": "boolean", "message": "string"}}, "submitAnswer": {"method": "POST", "url": "/api/UserBatchRecord/{userId}/submit-answer", "headers": {"Authorization": "Bearer {userToken}"}, "requestParams": {"path": {"userId": "string (required)"}, "body": {"batchId": "number (required)", "totalQuestions": "number (required, > 0)", "correctAnswers": "number (required, >= 0)", "answerDetails": "string (required, JSON format)"}}, "responseParams": {"success": "boolean", "data": "boolean", "message": "string"}}, "getAnswerStatus": {"method": "GET", "url": "/api/UserBatchRecord/{userId}/answer-status/{batchId}", "headers": {"Authorization": "Bearer {userToken}"}, "requestParams": {"path": {"userId": "string (required)", "batchId": "number (required)"}}, "responseParams": {"success": "boolean", "data": {"hasRecord": "boolean", "hasAnswered": "boolean", "totalQuestions": "number", "correctAnswers": "number", "correctRate": "number", "answerTime": "string", "canAnswer": "boolean"}, "message": "string"}}, "checkAnswerEligibility": {"method": "GET", "url": "/api/UserBatchRecord/{userId}/answer-eligibility/{batchId}", "headers": {"Authorization": "Bearer {userToken}"}, "requestParams": {"path": {"userId": "string (required)", "batchId": "number (required)"}}, "responseParams": {"success": "boolean", "data": {"canAnswer": "boolean", "reason": "string"}, "message": "string"}}, "grantReward": {"method": "POST", "url": "/api/UserBatchRecord/{userId}/grant-reward", "headers": {"Authorization": "Bearer {userToken}"}, "requestParams": {"path": {"userId": "string (required)"}, "body": {"batchId": "number (required)", "rewardAmount": "number (required, > 0)", "transactionId": "string (optional)", "outTradeNo": "string (optional)"}}, "responseParams": {"success": "boolean", "data": "boolean", "message": "string"}}, "getUserRecords": {"method": "GET", "url": "/api/UserBatchRecord/user/{userId}", "headers": {"Authorization": "Bearer {accessToken}"}, "requestParams": {"path": {"userId": "string (required)"}}, "responseParams": {"success": "boolean", "data": [{"id": "number", "userId": "string", "userNickname": "string", "batchId": "number", "batchName": "string", "videoCoverUrl": "string", "watchProgressPercent": "number", "isCompleted": "boolean", "hasAnswered": "boolean", "correctRate": "number", "rewardAmount": "number", "rewardStatus": "number", "createTime": "string", "lastWatchTime": "string"}], "message": "string"}}}, "wechatPayment": {"sendReward": {"method": "POST", "url": "/api/WechatPayment/send-reward", "headers": {"Authorization": "Bearer {accessToken}"}, "requestParams": {"body": {"rewardId": "number (required)", "userId": "string (required)", "openId": "string (required, max 100 chars)", "outTradeNo": "string (required, max 100 chars)", "amount": "number (required, > 0)", "currency": "string (optional, default: CNY)", "subject": "string (optional)", "body": "string (optional)", "paymentType": "string (optional)", "notifyUrl": "string (optional)", "returnUrl": "string (optional)"}}, "responseParams": {"success": "boolean", "data": "number", "message": "string"}}, "getRewardRecord": {"method": "GET", "url": "/api/WechatPayment/{paymentId}", "headers": {"Authorization": "Bearer {accessToken}"}, "requestParams": {"path": {"paymentId": "number (required)"}}, "responseParams": {"success": "boolean", "data": {"id": "number", "userId": "string", "orderNo": "string", "amount": "number", "currency": "string", "subject": "string", "body": "string", "paymentType": "string", "status": "number", "transactionId": "string", "payTime": "string", "refundNo": "string", "refundAmount": "number", "refundReason": "string", "refundTime": "string", "notifyUrl": "string", "returnUrl": "string", "createTime": "string"}, "message": "string"}}, "getMyRewards": {"method": "GET", "url": "/api/WechatPayment/my-rewards", "headers": {"Authorization": "Bearer {accessToken}"}, "requestParams": {"query": {"status": "number (optional, 0=待发放, 1=发放成功, 2=发放失败)"}}, "responseParams": {"success": "boolean", "data": [{"id": "number", "userId": "string", "orderNo": "string", "amount": "number", "currency": "string", "subject": "string", "body": "string", "paymentType": "string", "status": "number", "transactionId": "string", "payTime": "string", "refundNo": "string", "refundAmount": "number", "refundReason": "string", "refundTime": "string", "notifyUrl": "string", "returnUrl": "string", "createTime": "string"}], "message": "string"}}, "callback": {"method": "POST", "url": "/api/WechatPayment/callback", "requestParams": {"body": {"orderNo": "string (required)", "amount": "number (required)", "resultCode": "string (required)", "transactionId": "string (optional)", "payTime": "string (optional)"}}, "responseParams": {"success": "boolean", "data": "string", "message": "string"}}}, "user": {"getProfile": {"method": "GET", "url": "/api/User/profile", "headers": {"Authorization": "Bearer {accessToken}"}, "requestParams": {}, "responseParams": {"success": "boolean", "data": {"id": "string", "openId": "string", "unionId": "string", "nickname": "string", "avatar": "string", "employeeId": "string", "employeeName": "string", "lastLogin": "string", "createTime": "string", "updateTime": "string"}, "message": "string"}}, "getUser": {"method": "GET", "url": "/api/User/{userId}", "headers": {"Authorization": "Bearer {accessToken}"}, "requestParams": {"path": {"userId": "string (required)"}}, "responseParams": {"success": "boolean", "data": {"id": "string", "openId": "string", "unionId": "string", "nickname": "string", "avatar": "string", "employeeId": "string", "employeeName": "string", "lastLogin": "string", "createTime": "string", "updateTime": "string"}, "message": "string"}}, "getUserPagedList": {"method": "GET", "url": "/api/User", "headers": {"Authorization": "Bearer {accessToken}"}, "requestParams": {"query": {"nickname": "string (optional)", "employeeId": "string (optional)", "startTime": "string (optional)", "endTime": "string (optional)", "pageIndex": "number (default: 1)", "pageSize": "number (default: 20)"}}, "responseParams": {"success": "boolean", "data": {"items": [{"id": "string", "openId": "string", "unionId": "string", "nickname": "string", "avatar": "string", "employeeId": "string", "employeeName": "string", "lastLogin": "string", "createTime": "string", "updateTime": "string"}], "totalCount": "number", "pageIndex": "number", "pageSize": "number", "totalPages": "number"}, "message": "string"}}, "transferUsers": {"method": "POST", "url": "/api/User/transfer", "headers": {"Authorization": "Bearer {accessToken}"}, "requestParams": {"body": {"userIds": ["string"], "targetEmployeeId": "string (required)"}}, "responseParams": {"success": "boolean", "data": "boolean", "message": "string"}}, "accessPromotion": {"method": "POST", "url": "/api/User/access-promotion", "requestParams": {"body": {"promotionLink": "string (required)", "userAgent": "string (optional)", "ipAddress": "string (optional)"}}, "responseParams": {"success": "boolean", "data": {"accessId": "number", "promotionLinkId": "number", "accessTime": "string", "isNewAccess": "boolean"}, "message": "string"}}}}}
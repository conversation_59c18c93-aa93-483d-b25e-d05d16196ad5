import{_ as e,r as t,ab as a,b as s,X as i,Y as o,Z as n,$ as r,e as l,f as u,av as d,ac as c,d as p,ai as m,o as h,c as f,w as y,A as g,B as x,F as v,k as C,h as k,j as w,t as _,i as j,l as L,m as T,V as b,x as $,aJ as V}from"./index-BcNPWRTq.js";import{m as R,b as A}from"./media-common.vuYY10Yk.js";function D(e,t){return T.post(`/UserAudit/audit-user/${e}`,t)}const F=e({components:{AuditList:e({name:"AuditList",mixins:[R],props:{auditType:{type:String,required:!0},listData:{type:Array,required:!0},extraFields:{type:Array,default:()=>[]},typeLabel:{type:String,required:!0},loading:{type:Boolean,default:!1}},data:()=>({showConfirmPopup:!1,confirmType:"",currentItem:null,rejectReason:""}),methods:{getStatusText:e=>"approved"===e?"已通过":"rejected"===e?"已拒绝":"待审核",getStatusType:e=>"approved"===e?"success":"rejected"===e?"error":"warning",formatDate:e=>A(e),showConfirmModal(e,t){this.currentItem=e,this.confirmType=t,this.rejectReason="",this.showConfirmPopup=!0},cancelConfirm(){this.showConfirmPopup=!1,this.currentItem=null,this.confirmType="",this.rejectReason=""},handleConfirm(){"approve"===this.confirmType?this.$emit("approve",this.currentItem):this.$emit("reject",{item:this.currentItem,reason:this.rejectReason}),this.showConfirmPopup=!1,this.currentItem=null,this.confirmType="",this.rejectReason=""},viewDetail(e){this.$emit("view-detail",e)},getRoleText:e=>({employee:"员工",agent:"代理",user:"用户"}[e]||"未知"),getFieldValue(e,t){if(t.path&&"string"==typeof t.path){const a=t.path.split(".");let s=e;for(const e of a){if(null==s)return t.defaultValue||"无";s=s[e]}return s||t.defaultValue||"无"}return t.defaultValue||"无"}}},[["render",function(e,T,b,$,V,R){const A=t(s("u-loadmore"),a),D=t(s("u-avatar"),i),F=j,I=t(s("u-tag"),o),z=t(s("u-cell"),n),S=t(s("u-cell-group"),r),U=t(s("u-icon"),l),P=t(s("u-button"),u),q=t(s("u-card"),d),B=t(s("u-empty"),c),M=L,H=t(s("u-input"),p),J=t(s("u-modal"),m);return h(),f(F,{class:"audit-container"},{default:y((()=>[b.loading?(h(),f(A,{key:0,status:"loading",loadingText:"加载中..."})):(h(),f(F,{key:1,class:"audit-list"},{default:y((()=>[(h(!0),g(v,null,x(b.listData,((t,a)=>(h(),f(q,{key:t.id,padding:0,margin:"0 0 20rpx 0"},{head:y((()=>[k(F,{class:"card-header"},{default:y((()=>[k(F,{class:"user-basic-info"},{default:y((()=>[k(D,{src:e.buildCompleteFileUrl(t.avatar)||"/static/images/avatar-placeholder.png",size:"40",shape:"square"},null,8,["src"]),k(F,{class:"user-info"},{default:y((()=>[k(F,{class:"user-name"},{default:y((()=>[w(_(t.nickname||t.username||t.name||"未知用户"),1)])),_:2},1024),k(I,{text:R.getRoleText(t.role||b.auditType),type:"info",size:"mini",plain:!0},null,8,["text"])])),_:2},1024)])),_:2},1024),k(I,{text:R.getStatusText(t.status),type:R.getStatusType(t.status),size:"mini"},null,8,["text","type"])])),_:2},1024)])),body:y((()=>[k(F,{class:"card-body"},{default:y((()=>[b.extraFields.length>0?(h(),f(S,{key:0,border:!1},{default:y((()=>[(h(!0),g(v,null,x(b.extraFields,((e,a)=>(h(),f(z,{key:a,title:e.label,value:R.getFieldValue(t,e),border:!1,size:"small"},null,8,["title","value"])))),128))])),_:2},1024)):C("",!0)])),_:2},1024)])),foot:y((()=>[k(F,{class:"card-footer"},{default:y((()=>["pending"===t.status?(h(),g(v,{key:0},[k(P,{text:"拒绝",type:"info",plain:!0,size:"small",onClick:e=>R.showConfirmModal(t,"reject")},{icon:y((()=>[k(U,{name:"close",size:"14"})])),_:2},1032,["onClick"]),k(P,{text:"通过",type:"primary",size:"small",onClick:e=>R.showConfirmModal(t,"approve")},{icon:y((()=>[k(U,{name:"checkmark",size:"14"})])),_:2},1032,["onClick"])],64)):(h(),f(P,{key:1,text:"查看详情",type:"info",plain:!0,size:"small",onClick:e=>R.viewDetail(t)},{icon:y((()=>[k(U,{name:"eye",size:"14"})])),_:2},1032,["onClick"]))])),_:2},1024)])),_:2},1024)))),128)),0===b.listData.length?(h(),f(B,{key:0,mode:"data",text:`暂无${b.typeLabel}申请`},null,8,["text"])):C("",!0)])),_:1})),k(J,{modelValue:V.showConfirmPopup,"onUpdate:modelValue":T[1]||(T[1]=e=>V.showConfirmPopup=e),title:"确认"+("approve"===V.confirmType?"通过":"拒绝"),showCancelButton:!0,onConfirm:R.handleConfirm,onCancel:R.cancelConfirm},{default:y((()=>[k(F,{class:"modal-content"},{default:y((()=>[k(M,{class:"modal-text"},{default:y((()=>[w("确定要"+_("approve"===V.confirmType?"通过":"拒绝")+"该申请吗？",1)])),_:1}),"reject"===V.confirmType?(h(),f(H,{key:0,modelValue:V.rejectReason,"onUpdate:modelValue":T[0]||(T[0]=e=>V.rejectReason=e),placeholder:"请输入拒绝原因（可选）",type:"textarea",autoHeight:!0,maxlength:"200",style:{"margin-top":"20rpx"}},null,8,["modelValue"])):C("",!0)])),_:1})])),_:1},8,["modelValue","title","onConfirm","onCancel"])])),_:1})}],["__scopeId","data-v-07c17736"]])},data:()=>({userList:[],loading:!1,extraFields:[]}),onLoad(){this.loadUserList()},methods:{async loadUserList(){try{this.loading=!0;const e=await T.get("/UserAudit/pending-users");e.success&&e.data?this.userList=(e.data||[]).map((e=>({...e,status:this.convertStatus(e.status||0)}))):(this.$u.toast(e.msg||"获取用户列表失败"),this.userList=[])}catch(e){console.error("获取待审核用户列表失败:",e),this.$u.toast("网络错误，请稍后重试"),this.userList=[]}finally{this.loading=!1}},convertStatus:e=>({0:"pending",1:"approved",2:"rejected"}[e]||"pending"),async handleReject(e){try{const{item:t,reason:a}=e,s=await function(e,t=""){return D(e,{status:2,remark:t})}(t.id,a||"无");if(s.success){const e=this.userList.findIndex((e=>e.id===t.id));-1!==e&&this.userList.splice(e,1),this.$u.toast("已拒绝申请")}else this.$u.toast(s.msg||"操作失败")}catch(t){console.error("拒绝审核出错:",t),this.$u.toast("网络错误，请稍后重试")}},async handleApprove(e){try{const t=await function(e,t=""){return D(e,{status:1,remark:t})}(e.id);if(t.success){const t=this.userList.findIndex((t=>t.id===e.id));-1!==t&&this.userList.splice(t,1),this.$u.toast("已通过申请")}else this.$u.toast(t.msg||"操作失败")}catch(t){console.error("通过审核出错:",t),this.$u.toast("网络错误，请稍后重试")}},viewDetail(e){b({url:`/pages/admin/users/info?userId=${e.id}&type=user`})}}},[["render",function(e,a,i,o,n,r){const l=t(s("u-navbar"),V),u=$("AuditList"),d=j;return h(),f(d,{class:"container"},{default:y((()=>[k(l,{title:"用户审核",autoBack:!0,placeholder:!0}),k(u,{auditType:"user",listData:n.userList,extraFields:n.extraFields,typeLabel:"用户",loading:n.loading,onReject:r.handleReject,onApprove:r.handleApprove,onViewDetail:r.viewDetail},null,8,["listData","extraFields","loading","onReject","onApprove","onViewDetail"])])),_:1})}],["__scopeId","data-v-db34ab2a"]]);export{F as default};

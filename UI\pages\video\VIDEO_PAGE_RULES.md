# 视频页面开发规则

## ⚠️ 重要提醒

**每次修改视频页面前，必须严格遵循此规则！不得随意修改逻辑！**

## 📋 核心需求

### 1. 用户认证与审核

- **必须先进行用户认证**
- **审核状态检查**：
  - `auditStatus = 0`：待审核，显示审核弹窗，**禁止**加载视频
  - `auditStatus = 1`：已通过，允许加载视频
  - 其他状态：显示审核弹窗

### 2. 微信登录流程

- 支持微信网页授权登录
- 回调处理：检测 URL 中的 `code` 和 `state` 参数
- Token 保存：直接使用 `userToken` 字段

### 3. 视频数据加载

- **前提条件**：用户已登录且审核通过
- **只使用 batchId**：批次包含完整视频信息，不需要单独的 videoId
- 包含题目数据加载

### 4. 核心业务功能

- **观看进度记录**：实时记录用户观看时长和进度
- **答题系统**：视频结束后显示题目，用户答题
- **红包奖励**：答题完成后发放红包奖励
- **分享追踪**：记录分享人信息，用于奖励分配

## 📁 相关文件清单

### 核心文件

- `UI/pages/video/index.vue` - 主页面文件
- `UI/utils/wechatUserService.js` - 微信用户服务
- `UI/utils/wechatRequest.js` - 微信请求工具
- `UI/api/batch.js` - 批次 API
- `UI/mixins/media-common.js` - 媒体通用混入

### 配置文件

- `UI/static/config/app-config.js` - 应用配置
- `UI/utils/wechatWebAuth.js` - 微信网页授权
- `UI/pages.json` - 页面配置

### 组件文件

- `UI/components/VideoQuiz.vue` - 视频问答组件

## 🔄 标准流程

### 页面加载流程

```
onLoad(options)
  ↓
解析参数 (batchId, sharerId, code, state) - 注意：只需要batchId，不需要videoId
  ↓
检查是否微信回调 (code && state)
  ↓
是 → handleWechatCallback() → 检查审核状态 → 显示弹窗/加载视频+开始记录观看
  ↓
否 → authenticateUser() → 检查登录状态 → 微信授权/加载视频+开始记录观看
```

### 完整业务流程

```
用户认证通过 → 加载批次数据 → 显示视频 → 记录观看进度
  ↓
视频播放结束 → 显示题目 → 用户答题 → 提交答案
  ↓
答题完成 → 发放红包 → 记录完成状态 → 分享奖励处理
```

### 审核状态处理

```
获取用户信息
  ↓
检查 auditStatus
  ↓
auditStatus === 0 → 显示"待审核"弹窗 → 停止执行
  ↓
auditStatus === 1 → 继续加载视频数据
  ↓
其他状态 → 显示"审核未通过"弹窗 → 停止执行
```

## 📊 数据结构

### 页面数据

```javascript
data() {
  return {
    // 视频相关
    currentVideo: {
      url: '',
      cover: '',
      title: '',
      description: '',
      rewardAmount: 0
    },

    // 审核状态
    showAuditModal: false,
    auditUserInfo: null,

    // 问答相关
    showQuiz: false,
    quizData: { questions: [] },

    // 页面参数
    batchId: null,
    videoId: null,
    sharerId: null
  }
}
```

### 接口数据格式

```javascript
// 登录接口返回
{
  success: true,
  data: {
    userToken: "...",  // 主要token字段
    token: "...",      // 备用token字段
    userInfo: { ... },
    auditStatus: 0,    // 0=待审核, 1=已通过
    isNewUser: true
  }
}
```

## 🚫 禁止操作

1. **禁止删除审核状态检查逻辑**
2. **禁止在审核未通过时加载视频数据**
3. **禁止修改微信回调处理逻辑**
4. **禁止删除题目相关代码**
5. **禁止修改 Token 保存逻辑**

## ✅ 必须检查项

### 修改前检查

- [ ] 确认当前代码状态
- [ ] 理解修改需求
- [ ] 检查是否影响核心流程
- [ ] 备份关键逻辑

### 修改后检查

- [ ] 审核弹窗是否正常显示
- [ ] 视频数据是否正确加载
- [ ] 题目功能是否完整
- [ ] 微信登录是否正常
- [ ] 控制台日志是否清晰

## 🔧 关键方法

### 必须实现的方法

#### 核心流程方法

- `onLoad(options)` - 页面加载入口（只解析 batchId, sharerId, code, state）
- `handleWechatCallback(options)` - 微信回调处理
- `authenticateUser()` - 用户认证
- `loadVideoData()` - 视频数据加载
- `loadDataFromBatch()` - 批次数据加载（只需要这个方法）

#### 观看记录方法

- `startWatchRecord()` - 开始记录观看
- `onTimeUpdate(e)` - 视频时间更新，记录观看进度
- `updateWatchProgress()` - 更新观看进度到服务器
- `onVideoEnded()` - 视频播放结束

#### 答题系统方法

- `showQuizAfterVideo()` - 视频结束后显示题目
- `onQuizSubmit(answers)` - 提交答题结果
- `onQuizComplete(result)` - 答题完成处理

#### 红包奖励方法

- `claimReward()` - 领取红包奖励
- `recordSharerReward()` - 记录分享人奖励

### 关键检查点

```javascript
// 审核状态检查（关键！）
if (result.data.auditStatus !== 1) {
  this.auditUserInfo = result.data.userInfo
  this.showAuditModal = true
  return // 必须停止执行
}
```

## 📝 调试日志规范

### 必须的日志

```javascript
console.log('视频页面加载，参数:', options)
console.log('检测到微信回调参数，处理微信登录')
console.log('用户审核状态:', result.data.auditStatus)
console.log('showAuditModal 设置为:', this.showAuditModal)
```

## ⚡ 紧急修复指南

如果页面出现问题：

1. 检查控制台日志
2. 确认审核状态检查逻辑
3. 验证 showAuditModal 状态
4. 检查视频数据加载条件
5. 恢复备份的关键逻辑

---

**⚠️ 警告：违反此规则导致的问题，必须立即回滚并重新按规则修改！**

import{aF as e,a as r,aG as s,aH as t,aI as n}from"./index-BcNPWRTq.js";const i={data:()=>({currentUser:null,currentUserType:null,currentPermissions:[]}),computed:{isAdmin(){return"admin"===this.currentUserType||"super_admin"===this.currentUserType},isManager(){return["admin","super_admin","manager","agent"].includes(this.currentUserType)},isEmployee(){return["admin","super_admin","manager","agent","employee"].includes(this.currentUserType)},isUser(){return"user"===this.currentUserType},currentPermissionLevel(){return e[this.currentUserType]||0}},created(){this.initPermissionData()},methods:{initPermissionData(){this.currentUser=r.getLoginInfo(),this.currentUserType=r.getUserType(),this.currentPermissions=this.getCurrentPermissions()},getCurrentPermissions(){return this.currentUserType&&s[this.currentUserType]||[]},hasPermission(e){return!!this.currentUserType&&(!!this.currentPermissions.includes("*")||this.currentPermissions.includes(e))},hasRole(e){return!!this.currentUserType&&(Array.isArray(e)?e.includes(this.currentUserType):this.currentUserType===e)},hasPermissionLevel(r){if(!this.currentUserType)return!1;return(e[this.currentUserType]||0)>=(e[r]||0)},canAccessPage(e){if(!this.currentUserType)return!1;if("admin"===this.currentUserType||"super_admin"===this.currentUserType)return!0;const r=t[e];return!r||r.includes(this.currentUserType)},canUseFeature(e){if(!this.currentUserType)return!1;const r={user_audit:["employee"],manage_users:["employee"],create_user:["employee"],edit_user:["employee"],disable_user:["employee"]};if(r[e])return r[e].includes(this.currentUserType);const s=n[e];return!!s&&("admin"===this.currentUserType||"super_admin"===this.currentUserType||s.includes(this.currentUserType))},canManageUser(e){return!(!this.currentUserType||!e)&&("admin"===this.currentUserType||"super_admin"===this.currentUserType?"super_admin"!==e.userType||"super_admin"===this.currentUserType:"manager"===this.currentUserType||"agent"===this.currentUserType?["employee","user"].includes(e.userType):"employee"===this.currentUserType&&"user"===e.userType)},canViewUser(e){var r;return!(!this.currentUserType||!e)&&(!!["admin","super_admin","manager","agent"].includes(this.currentUserType)||("employee"===this.currentUserType?["user"].includes(e.userType):"user"===this.currentUserType&&e.id===(null==(r=this.currentUser)?void 0:r.userId)))},getUserTypeName:e=>({super_admin:"超级管理员",admin:"管理员",manager:"管理",agent:"管理",employee:"员工",user:"用户"}[e]||"未知"),refreshPermissionData(){this.initPermissionData()}}};export{i as p};

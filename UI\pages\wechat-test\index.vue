<template>
  <view class="test-container">
    <view class="test-content">
      <!-- 页面标题 -->
      <view class="header-section">
        <text class="page-title">微信登录测试</text>
        <text class="page-subtitle">网页端微信授权登录功能测试</text>
      </view>

      <!-- 环境信息 -->
      <view class="info-section">
        <view class="info-card">
          <text class="info-title">当前环境信息</text>
          <view class="info-item">
            <text class="info-label">平台：</text>
            <text class="info-value">{{ platformInfo.platform }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">是否H5：</text>
            <text class="info-value">{{ platformInfo.isH5 ? '是' : '否' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">是否微信浏览器：</text>
            <text class="info-value">{{ platformInfo.isWechatBrowser ? '是' : '否' }}</text>
          </view>
          <view class="info-item">
            <text class="info-label">用户代理：</text>
            <text class="info-value small">{{ platformInfo.userAgent }}</text>
          </view>
        </view>
      </view>

      <!-- 微信配置信息 -->
      <view class="config-section">
        <view class="config-card">
          <text class="config-title">微信配置信息</text>
          <view class="config-item">
            <text class="config-label">AppID：</text>
            <text class="config-value">{{ wechatConfig.appId }}</text>
          </view>
          <view class="config-item">
            <text class="config-label">授权范围：</text>
            <text class="config-value">{{ wechatConfig.scope }}</text>
          </view>
          <view class="config-item">
            <text class="config-label">回调地址：</text>
            <text class="config-value small">{{ wechatConfig.redirectUri }}</text>
          </view>
        </view>
      </view>

      <!-- 登录状态 -->
      <view class="status-section">
        <view class="status-card">
          <text class="status-title">登录状态</text>
          <view v-if="userInfo" class="user-info">
            <view class="status-item">
              <text class="status-label">用户昵称：</text>
              <text class="status-value">{{ userInfo.nickname || '未知' }}</text>
            </view>
            <view class="status-item">
              <text class="status-label">用户ID：</text>
              <text class="status-value">{{ userInfo.id || '未知' }}</text>
            </view>
            <view class="status-item">
              <text class="status-label">登录时间：</text>
              <text class="status-value">{{ loginTime }}</text>
            </view>
          </view>
          <view v-else class="no-user">
            <text class="no-user-text">未登录</text>
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="action-section">
        <u-button v-if="!userInfo" type="success" @click="handleWechatLogin" :disabled="isLoading" :loading="isLoading"
          icon="weixin" class="login-btn">
          {{ isLoading ? '正在跳转...' : '微信登录' }}
        </u-button>

        <view v-else class="logged-actions">
          <u-button type="warning" @click="handleLogout" class="logout-btn">
            退出登录
          </u-button>
          <u-button type="info" @click="refreshUserInfo" class="refresh-btn">
            刷新信息
          </u-button>
        </view>

        <u-button type="primary" @click="testAuthUrl" class="test-btn">
          测试授权URL生成（后端接口）
        </u-button>

        <u-button type="default" @click="testAuthUrlDirect" class="test-btn">
          测试授权URL生成（直接构造）
        </u-button>

        <u-button type="warning" @click="testDirectJump" class="test-btn">
          测试直接跳转到授权页面
        </u-button>

        <u-button type="error" @click="testForceJump" class="test-btn">
          强制跳转测试（多种方式）
        </u-button>

        <u-button type="success" @click="generateClickableLink" class="test-btn">
          生成可点击的授权链接
        </u-button>

        <u-button type="info" @click="validateAuthUrl" class="test-btn">
          验证授权URL是否可访问
        </u-button>

        <u-button type="primary" @click="forceJumpToAuth" class="test-btn" style="background: #ff6b35;">
          🚀 强制跳转到微信授权（最新版）
        </u-button>

        <!-- 显示可点击的授权链接 -->
        <view v-if="authLinkUrl" class="auth-link-container">
          <text class="auth-link-title">请点击下面的链接进行微信授权：</text>
          <view class="auth-link-wrapper">
            <text class="auth-link" @click="openAuthLink">{{ authLinkUrl }}</text>
          </view>
          <u-button type="primary" @click="openAuthLink" class="auth-link-btn">
            点击进行微信授权
          </u-button>
        </view>

        <!-- 显示微信授权回调调试信息 -->
        <view v-if="debugInfo" class="debug-info-container">
          <text class="debug-info-title">🔍 微信授权回调调试信息</text>
          <view class="debug-info-content">
            <view class="debug-item">
              <text class="debug-label">授权状态:</text>
              <text :class="['debug-value', debugInfo.success ? 'success' : 'error']">
                {{ debugInfo.success ? '✅ 成功' : '❌ 失败' }}
              </text>
            </view>
            <view v-if="debugInfo.code" class="debug-item">
              <text class="debug-label">授权码(code):</text>
              <text class="debug-value code">{{ debugInfo.code }}</text>
            </view>
            <view v-if="debugInfo.state" class="debug-item">
              <text class="debug-label">状态参数(state):</text>
              <text class="debug-value">{{ debugInfo.state }}</text>
            </view>
            <view v-if="debugInfo.stateData" class="debug-item">
              <text class="debug-label">状态数据:</text>
              <text class="debug-value">{{ JSON.stringify(debugInfo.stateData) }}</text>
            </view>
            <view v-if="debugInfo.error" class="debug-item">
              <text class="debug-label">错误信息:</text>
              <text class="debug-value error">{{ debugInfo.error }}</text>
            </view>
            <view v-if="debugInfo.message" class="debug-item">
              <text class="debug-label">详细信息:</text>
              <text class="debug-value">{{ debugInfo.message }}</text>
            </view>
            <view class="debug-item">
              <text class="debug-label">回调时间:</text>
              <text class="debug-value">{{ debugInfo.timestamp }}</text>
            </view>
          </view>
          <u-button type="warning" @click="clearDebugInfo" class="clear-debug-btn">
            清除调试信息
          </u-button>
        </view>
      </view>

      <!-- 测试日志 -->
      <view class="log-section">
        <view class="log-card">
          <view class="log-header">
            <text class="log-title">测试日志</text>
            <view class="log-actions">
              <u-button type="primary" size="mini" @click="copyAllLogs">复制全部</u-button>
              <u-button type="default" size="mini" @click="clearLogs">清空</u-button>
            </view>
          </view>
          <scroll-view class="log-content" scroll-y>
            <view v-for="(log, index) in logs" :key="index" class="log-item">
              <text class="log-time">{{ log.time }}</text>
              <text class="log-message" :class="log.type">{{ log.message }}</text>
            </view>
            <view v-if="logs.length === 0" class="no-logs">
              <text class="no-logs-text">暂无日志</text>
            </view>
          </scroll-view>
        </view>
      </view>
    </view>

    <!-- Toast -->
    <u-toast ref="uToast" />
  </view>
</template>

<script>
import wechatWebAuth, { setLogCallback } from '@/utils/wechatWebAuth.js'

export default {
  data () {
    return {
      isLoading: false,
      userInfo: null,
      loginTime: '',
      platformInfo: {
        platform: '',
        isH5: false,
        isWechatBrowser: false,
        userAgent: ''
      },
      wechatConfig: {
        appId: '',
        scope: '',
        redirectUri: ''
      },
      logs: [],
      // 授权链接URL
      authLinkUrl: '',
      // 调试信息
      debugInfo: null
    }
  },

  onLoad (options) {
    // 添加全局错误监听
    // #ifdef H5
    window.addEventListener('error', (event) => {
      this.addLog(`全局错误: ${event.error?.message || event.message}`, 'error')
      console.error('全局错误事件:', event)
    })

    window.addEventListener('unhandledrejection', (event) => {
      this.addLog(`未处理的Promise拒绝: ${event.reason?.message || event.reason}`, 'error')
      console.error('未处理的Promise拒绝:', event)
    })
    // #endif

    // 设置微信授权工具的日志回调
    setLogCallback((message, type) => {
      this.addLog(message, type)
    })

    // 检查是否有调试信息
    if (options.debug) {
      try {
        this.debugInfo = JSON.parse(decodeURIComponent(options.debug))
        this.addLog('收到微信授权回调调试信息', 'success')
        this.addLog(`调试信息: ${JSON.stringify(this.debugInfo)}`, 'info')
      } catch (e) {
        this.addLog('解析调试信息失败: ' + e.message, 'error')
      }
    }

    this.initPage()
  },

  methods: {
    /**
     * 初始化页面
     */
    initPage () {
      this.addLog('页面初始化开始', 'info')

      // 获取平台信息
      this.getPlatformInfo()

      // 获取微信配置
      this.getWechatConfig()

      // 检查登录状态
      this.checkLoginStatus()

      this.addLog('页面初始化完成', 'success')
    },

    /**
     * 获取平台信息
     */
    getPlatformInfo () {
      try {
        const systemInfo = uni.getSystemInfoSync()

        this.platformInfo = {
          platform: systemInfo.platform || 'unknown',
          isH5: wechatWebAuth.isH5Environment(),
          isWechatBrowser: wechatWebAuth.isWechatBrowser(),
          userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : 'N/A'
        }

        this.addLog(`平台信息获取成功: ${this.platformInfo.platform}`, 'info')
      } catch (error) {
        this.addLog(`获取平台信息失败: ${error.message}`, 'error')
      }
    },

    /**
     * 获取微信配置
     */
    getWechatConfig () {
      try {
        // 验证配置
        const validation = wechatWebAuth.validateConfig()

        if (validation.valid) {
          this.wechatConfig = {
            appId: wechatWebAuth.config.appId,
            scope: wechatWebAuth.config.scope,
            redirectUri: wechatWebAuth.getRedirectUri()
          }
          this.addLog('微信配置加载成功', 'success')
        } else {
          this.addLog(`微信配置验证失败: ${validation.errors.join(', ')}`, 'error')
        }
      } catch (error) {
        this.addLog(`获取微信配置失败: ${error.message}`, 'error')
      }
    },

    /**
     * 检查登录状态
     */
    checkLoginStatus () {
      try {
        const userInfo = uni.getStorageSync('wechatUserInfo')
        const token = uni.getStorageSync('wechatUserToken')

        if (userInfo && token) {
          this.userInfo = userInfo
          this.loginTime = new Date().toLocaleString()
          this.addLog('发现已登录用户信息', 'success')
        } else {
          this.addLog('未发现登录信息', 'info')
        }
      } catch (error) {
        this.addLog(`检查登录状态失败: ${error.message}`, 'error')
      }
    },

    /**
     * 处理微信登录
     */
    async handleWechatLogin () {
      try {
        this.isLoading = true
        this.addLog('=== 开始微信登录流程 ===', 'info')

        // 检查window对象
        this.addLog(`window对象存在: ${typeof window !== 'undefined'}`, 'info')
        if (typeof window !== 'undefined') {
          this.addLog(`当前URL: ${window.location.href}`, 'info')
        }

        // 检查wechatWebAuth对象
        this.addLog(`wechatWebAuth对象存在: ${!!wechatWebAuth}`, 'info')
        this.addLog(`wechatWebAuth类型: ${typeof wechatWebAuth}`, 'info')

        // 检查环境
        this.addLog(`当前环境检查: isH5=${this.platformInfo.isH5}, platform=${this.platformInfo.platform}`, 'info')

        if (!this.platformInfo.isH5) {
          throw new Error('当前环境不支持网页端微信登录')
        }

        // 检查微信配置
        this.addLog('检查微信配置...', 'info')
        this.addLog(`wechatWebAuth.config: ${JSON.stringify(wechatWebAuth.config)}`, 'info')

        const validation = wechatWebAuth.validateConfig()
        this.addLog(`配置验证结果: ${JSON.stringify(validation)}`, 'info')

        if (!validation.valid) {
          this.addLog(`微信配置验证失败: ${validation.errors.join(', ')}`, 'error')
          throw new Error(`微信配置错误: ${validation.errors.join(', ')}`)
        }
        this.addLog('微信配置验证通过', 'success')

        // 生成状态参数
        const extraState = {
          returnUrl: '/pages/wechat-test/index',
          timestamp: Date.now()
        }
        this.addLog(`生成状态参数: ${JSON.stringify(extraState)}`, 'info')

        // 获取回调地址
        const redirectUri = wechatWebAuth.getRedirectUri()
        this.addLog(`回调地址: ${redirectUri}`, 'info')

        // 检查startAuth方法
        this.addLog(`startAuth方法存在: ${typeof wechatWebAuth.startAuth === 'function'}`, 'info')

        this.addLog('准备调用startAuth方法', 'info')

        // 启动授权流程（先尝试直接构造方式）
        const authOptions = {
          extraState,
          useDirect: true // 先使用直接构造方式
        }
        this.addLog(`startAuth参数: ${JSON.stringify(authOptions)}`, 'info')

        await wechatWebAuth.startAuth(authOptions)

        this.addLog('startAuth方法调用完成', 'success')

      } catch (error) {
        this.addLog(`微信登录失败: ${error.message}`, 'error')
        this.addLog(`错误名称: ${error.name}`, 'error')
        this.addLog(`错误堆栈: ${error.stack}`, 'error')
        this.showToastMessage(error.message, 'error')
        console.error('微信登录详细错误:', error)
      } finally {
        this.isLoading = false
        this.addLog('=== 微信登录流程结束 ===', 'info')
      }
    },

    /**
     * 测试授权URL生成（后端接口）
     */
    async testAuthUrl () {
      try {
        this.addLog('开始测试授权URL生成（后端接口）', 'info')

        const authUrl = await wechatWebAuth.buildAuthUrl({
          state: wechatWebAuth.generateState({ test: true })
        })

        this.addLog(`生成的授权URL: ${authUrl}`, 'success')
        this.showToastMessage('授权URL已生成，请查看日志', 'success')

        // 复制到剪贴板（如果支持）
        // #ifdef H5
        if (navigator.clipboard) {
          navigator.clipboard.writeText(authUrl).then(() => {
            this.addLog('授权URL已复制到剪贴板', 'success')
          }).catch(() => {
            this.addLog('复制到剪贴板失败', 'warning')
          })
        }
        // #endif

      } catch (error) {
        this.addLog(`生成授权URL失败: ${error.message}`, 'error')
        this.showToastMessage(error.message, 'error')
      }
    },

    /**
     * 测试授权URL生成（直接构造）
     */
    testAuthUrlDirect () {
      try {
        this.addLog('开始测试授权URL生成（直接构造）', 'info')

        const authUrl = wechatWebAuth.buildAuthUrlDirect({
          state: wechatWebAuth.generateState({ test: true })
        })

        this.addLog(`生成的授权URL: ${authUrl}`, 'success')
        this.showToastMessage('授权URL已生成，请查看日志', 'success')

        // 复制到剪贴板（如果支持）
        // #ifdef H5
        if (navigator.clipboard) {
          navigator.clipboard.writeText(authUrl).then(() => {
            this.addLog('授权URL已复制到剪贴板', 'success')
          }).catch(() => {
            this.addLog('复制到剪贴板失败', 'warning')
          })
        }
        // #endif

      } catch (error) {
        this.addLog(`生成授权URL失败: ${error.message}`, 'error')
        this.showToastMessage(error.message, 'error')
      }
    },

    /**
     * 测试直接跳转到授权页面
     */
    testDirectJump () {
      try {
        this.addLog('开始测试直接跳转', 'info')

        const authUrl = wechatWebAuth.buildAuthUrlDirect({
          state: wechatWebAuth.generateState({ test: true })
        })

        this.addLog(`生成的授权URL: ${authUrl}`, 'success')

        // 直接跳转
        this.addLog('执行直接跳转...', 'info')
        this.addLog(`跳转前URL: ${window.location.href}`, 'info')

        // 使用多种方式尝试跳转
        try {
          // 方式1: 直接赋值
          window.location.href = authUrl
          this.addLog('方式1: window.location.href 已执行', 'info')
        } catch (e) {
          this.addLog(`方式1失败: ${e.message}`, 'error')

          try {
            // 方式2: 使用replace
            window.location.replace(authUrl)
            this.addLog('方式2: window.location.replace 已执行', 'info')
          } catch (e2) {
            this.addLog(`方式2失败: ${e2.message}`, 'error')

            try {
              // 方式3: 使用assign
              window.location.assign(authUrl)
              this.addLog('方式3: window.location.assign 已执行', 'info')
            } catch (e3) {
              this.addLog(`方式3失败: ${e3.message}`, 'error')
            }
          }
        }

        // 检查是否跳转成功
        setTimeout(() => {
          this.addLog(`跳转后URL: ${window.location.href}`, 'info')
          if (window.location.href === authUrl || window.location.href.includes('open.weixin.qq.com')) {
            this.addLog('跳转成功！', 'success')
          } else {
            this.addLog('跳转可能失败，URL未改变', 'warning')
          }
        }, 500)

      } catch (error) {
        this.addLog(`测试直接跳转失败: ${error.message}`, 'error')
        this.showToastMessage(error.message, 'error')
      }
    },

    /**
     * 强制跳转测试（多种方式）
     */
    testForceJump () {
      try {
        this.addLog('开始强制跳转测试', 'info')

        const authUrl = wechatWebAuth.buildAuthUrlDirect({
          state: wechatWebAuth.generateState({ test: true })
        })

        this.addLog(`目标URL: ${authUrl}`, 'info')
        this.addLog(`当前URL: ${window.location.href}`, 'info')
        this.addLog(`是否微信浏览器: ${wechatWebAuth.isWechatBrowser()}`, 'info')

        // 方式1: 创建a标签点击
        this.addLog('尝试方式1: a标签点击', 'info')
        try {
          const link = document.createElement('a')
          link.href = authUrl
          link.target = '_self'
          link.style.display = 'none'
          document.body.appendChild(link)

          // 模拟用户点击
          const clickEvent = new MouseEvent('click', {
            view: window,
            bubbles: true,
            cancelable: true
          })
          link.dispatchEvent(clickEvent)

          document.body.removeChild(link)
          this.addLog('方式1: a标签点击已执行', 'success')

          setTimeout(() => {
            this.addLog(`方式1后URL: ${window.location.href}`, 'info')
          }, 200)

        } catch (e) {
          this.addLog(`方式1失败: ${e.message}`, 'error')
        }

        // 等待一段时间再尝试其他方式
        setTimeout(() => {
          if (!window.location.href.includes('open.weixin.qq.com')) {
            this.addLog('方式1未成功，尝试方式2: location.replace', 'info')
            try {
              window.location.replace(authUrl)
              this.addLog('方式2: location.replace 已执行', 'success')
            } catch (e) {
              this.addLog(`方式2失败: ${e.message}`, 'error')

              // 方式3: 使用top.location
              setTimeout(() => {
                if (!window.location.href.includes('open.weixin.qq.com')) {
                  this.addLog('方式2未成功，尝试方式3: top.location', 'info')
                  try {
                    if (window.top && window.top !== window) {
                      window.top.location.href = authUrl
                      this.addLog('方式3: top.location 已执行', 'success')
                    } else {
                      this.addLog('方式3: 当前就是顶层窗口', 'info')

                      // 方式4: 使用parent.location
                      if (window.parent && window.parent !== window) {
                        window.parent.location.href = authUrl
                        this.addLog('方式4: parent.location 已执行', 'success')
                      } else {
                        this.addLog('方式4: 当前就是父窗口', 'info')

                        // 最后尝试: 直接赋值
                        window.location.href = authUrl
                        this.addLog('最后尝试: 直接赋值已执行', 'info')
                      }
                    }
                  } catch (e) {
                    this.addLog(`方式3失败: ${e.message}`, 'error')
                  }
                }
              }, 300)
            }
          }
        }, 300)

        // 最终检查
        setTimeout(() => {
          this.addLog(`最终URL: ${window.location.href}`, 'info')
          if (window.location.href.includes('open.weixin.qq.com')) {
            this.addLog('强制跳转成功！', 'success')
          } else {
            this.addLog('所有跳转方式都失败了', 'error')
            this.addLog('可能是微信浏览器的安全限制', 'warning')
          }
        }, 1000)

      } catch (error) {
        this.addLog(`强制跳转测试失败: ${error.message}`, 'error')
        this.showToastMessage(error.message, 'error')
      }
    },

    /**
     * 生成可点击的授权链接
     */
    generateClickableLink () {
      try {
        this.addLog('生成可点击的授权链接', 'info')

        const authUrl = wechatWebAuth.buildAuthUrlDirect({
          state: wechatWebAuth.generateState({
            returnUrl: '/pages/wechat-test/index',
            timestamp: Date.now()
          })
        })

        this.authLinkUrl = authUrl
        this.addLog(`授权链接已生成: ${authUrl}`, 'success')
        this.addLog('请点击下方的蓝色按钮进行授权', 'info')

      } catch (error) {
        this.addLog(`生成授权链接失败: ${error.message}`, 'error')
        this.showToastMessage(error.message, 'error')
      }
    },

    /**
     * 打开授权链接
     */
    openAuthLink () {
      try {
        if (!this.authLinkUrl) {
          this.showToastMessage('请先生成授权链接', 'warning')
          return
        }

        this.addLog('用户点击授权链接', 'info')
        this.addLog(`即将跳转到: ${this.authLinkUrl}`, 'info')

        // 直接赋值跳转
        window.location.href = this.authLinkUrl

      } catch (error) {
        this.addLog(`打开授权链接失败: ${error.message}`, 'error')
        this.showToastMessage(error.message, 'error')
      }
    },

    /**
     * 验证授权URL是否可访问
     */
    async validateAuthUrl () {
      try {
        this.addLog('开始验证授权URL', 'info')

        const authUrl = wechatWebAuth.buildAuthUrlDirect({
          state: wechatWebAuth.generateState({ test: true })
        })

        this.addLog(`验证URL: ${authUrl}`, 'info')

        // 检查URL格式
        try {
          const url = new URL(authUrl)
          this.addLog(`URL协议: ${url.protocol}`, 'info')
          this.addLog(`URL主机: ${url.host}`, 'info')
          this.addLog(`URL路径: ${url.pathname}`, 'info')
          this.addLog(`URL参数: ${url.search}`, 'info')
          this.addLog(`URL哈希: ${url.hash}`, 'info')

          // 检查必要参数
          const params = new URLSearchParams(url.search)
          const requiredParams = ['appid', 'redirect_uri', 'response_type', 'scope', 'state']

          for (const param of requiredParams) {
            const value = params.get(param)
            if (value) {
              this.addLog(`参数 ${param}: ${value}`, 'success')
            } else {
              this.addLog(`缺少参数 ${param}`, 'error')
            }
          }

          this.addLog('URL格式验证通过', 'success')

        } catch (urlError) {
          this.addLog(`URL格式错误: ${urlError.message}`, 'error')
        }

        // 提示用户手动验证
        this.addLog('请复制上面的URL到新标签页手动打开，查看是否能正常访问微信授权页面', 'info')
        this.addLog('如果显示"参数错误"或"域名未配置"，说明需要在微信公众平台配置域名', 'warning')

      } catch (error) {
        this.addLog(`验证授权URL失败: ${error.message}`, 'error')
        this.showToastMessage(error.message, 'error')
      }
    },

    /**
     * 强制跳转到微信授权（最新版）
     */
    async forceJumpToAuth () {
      try {
        this.addLog('=== 强制跳转到微信授权（最新版）===', 'info')

        const authUrl = wechatWebAuth.buildAuthUrlDirect({
          state: wechatWebAuth.generateState({
            returnUrl: '/pages/wechat-test/index',
            timestamp: Date.now()
          })
        })

        this.addLog(`生成授权URL: ${authUrl}`, 'success')
        this.addLog(`当前页面: ${window.location.href}`, 'info')

        // 直接强制跳转，不使用工具类的复杂逻辑
        this.addLog('开始强制跳转...', 'info')

        // 最简单直接的跳转
        window.location.href = authUrl

        // 如果3秒后还在当前页面，说明跳转失败
        setTimeout(() => {
          if (window.location.href.includes('wechat-test')) {
            this.addLog('跳转可能失败，尝试其他方式...', 'warning')

            // 尝试其他方式
            try {
              window.location.replace(authUrl)
            } catch (e) {
              this.addLog(`replace方式也失败: ${e.message}`, 'error')

              // 最后尝试
              try {
                window.open(authUrl, '_self')
              } catch (e2) {
                this.addLog(`open方式也失败: ${e2.message}`, 'error')
                this.addLog('所有跳转方式都失败，可能是微信浏览器限制', 'error')
              }
            }
          }
        }, 3000)

      } catch (error) {
        this.addLog(`强制跳转失败: ${error.message}`, 'error')
        this.showToastMessage(error.message, 'error')
      }
    },

    /**
     * 清除调试信息
     */
    clearDebugInfo () {
      this.debugInfo = null
      this.addLog('调试信息已清除', 'info')
    },

    /**
     * 退出登录
     */
    handleLogout () {
      try {
        uni.removeStorageSync('wechatUserInfo')
        uni.removeStorageSync('wechatUserToken')

        this.userInfo = null
        this.loginTime = ''

        this.addLog('退出登录成功', 'success')
        this.showToastMessage('已退出登录', 'success')
      } catch (error) {
        this.addLog(`退出登录失败: ${error.message}`, 'error')
        this.showToastMessage(error.message, 'error')
      }
    },

    /**
     * 刷新用户信息
     */
    refreshUserInfo () {
      this.checkLoginStatus()
      this.showToastMessage('用户信息已刷新', 'success')
    },

    /**
     * 添加日志
     */
    addLog (message, type = 'info') {
      const log = {
        time: new Date().toLocaleTimeString(),
        message: message,
        type: type
      }

      this.logs.unshift(log)

      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50)
      }

      console.log(`[${type.toUpperCase()}] ${message}`)
    },

    /**
     * 复制所有日志
     */
    async copyAllLogs () {
      try {
        if (this.logs.length === 0) {
          this.showToastMessage('暂无日志可复制', 'warning')
          return
        }

        // 格式化日志内容
        const logContent = this.logs.map(log => {
          return `[${log.time}] [${log.type.toUpperCase()}] ${log.message}`
        }).reverse().join('\n')

        // 添加环境信息
        const envInfo = `
=== 环境信息 ===
平台: ${this.platformInfo.platform}
是否H5: ${this.platformInfo.isH5}
是否微信浏览器: ${this.platformInfo.isWechatBrowser}
用户代理: ${this.platformInfo.userAgent}

=== 微信配置 ===
AppID: ${this.wechatConfig.appId}
授权范围: ${this.wechatConfig.scope}
回调地址: ${this.wechatConfig.redirectUri}

=== 日志记录 ===
${logContent}
`

        // 复制到剪贴板
        // #ifdef H5
        if (navigator.clipboard) {
          await navigator.clipboard.writeText(envInfo)
          this.showToastMessage('所有日志已复制到剪贴板', 'success')
          this.addLog('日志已复制到剪贴板', 'success')
        } else {
          // 备用方案：创建临时文本区域
          const textArea = document.createElement('textarea')
          textArea.value = envInfo
          document.body.appendChild(textArea)
          textArea.select()
          document.execCommand('copy')
          document.body.removeChild(textArea)
          this.showToastMessage('所有日志已复制到剪贴板', 'success')
          this.addLog('日志已复制到剪贴板（备用方案）', 'success')
        }
        // #endif

        // #ifndef H5
        // 非H5环境，显示日志内容供手动复制
        uni.showModal({
          title: '日志内容',
          content: envInfo.substring(0, 500) + '...',
          showCancel: false,
          confirmText: '知道了'
        })
        // #endif

      } catch (error) {
        console.error('复制日志失败:', error)
        this.addLog(`复制日志失败: ${error.message}`, 'error')
        this.showToastMessage('复制失败: ' + error.message, 'error')
      }
    },

    /**
     * 清空日志
     */
    clearLogs () {
      this.logs = []
      this.showToastMessage('日志已清空', 'success')
    },

    /**
     * 显示Toast消息
     */
    showToastMessage (message, type = 'success') {
      this.$refs.uToast.show({
        message: message,
        type: type,
        duration: 3000
      })
    }
  }
}
</script>

<style scoped>
.test-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20rpx;
}

.test-content {
  max-width: 750rpx;
  margin: 0 auto;
}

/* 页面标题 */
.header-section {
  text-align: center;
  margin-bottom: 30rpx;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.page-subtitle {
  font-size: 26rpx;
  color: #666;
  display: block;
}

/* 信息卡片通用样式 */
.info-section,
.config-section,
.status-section {
  margin-bottom: 30rpx;
}

.info-card,
.config-card,
.status-card {
  background: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.info-title,
.config-title,
.status-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.info-item,
.config-item,
.status-item {
  display: flex;
  margin-bottom: 15rpx;
  align-items: flex-start;
}

.info-label,
.config-label,
.status-label {
  font-size: 28rpx;
  color: #666;
  width: 140rpx;
  flex-shrink: 0;
}

.info-value,
.config-value,
.status-value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
}

.info-value.small,
.config-value.small {
  font-size: 24rpx;
  line-height: 1.4;
}

/* 用户信息 */
.user-info {
  border: 2rpx solid #52C41A;
  border-radius: 8rpx;
  padding: 20rpx;
  background: #f6ffed;
}

.no-user {
  text-align: center;
  padding: 40rpx;
}

.no-user-text {
  font-size: 28rpx;
  color: #999;
}

/* 操作按钮 */
.action-section {
  margin-bottom: 30rpx;
}

.login-btn,
.logout-btn,
.refresh-btn,
.test-btn {
  width: 100%;
  margin-bottom: 20rpx;
}

.logged-actions {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.logged-actions .logout-btn,
.logged-actions .refresh-btn {
  flex: 1;
  margin-bottom: 0;
}

/* 日志区域 */
.log-section {
  margin-bottom: 30rpx;
}

.log-card {
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.log-actions {
  display: flex;
  gap: 10rpx;
}

/* 授权链接样式 */
.auth-link-container {
  margin: 30rpx 0;
  padding: 30rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
  border: 2rpx solid #e9ecef;
}

.auth-link-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.auth-link-wrapper {
  margin: 20rpx 0;
  padding: 20rpx;
  background: #fff;
  border-radius: 8rpx;
  border: 1rpx solid #ddd;
}

.auth-link {
  font-size: 24rpx;
  color: #007bff;
  word-break: break-all;
  line-height: 1.5;
}

.auth-link-btn {
  margin-top: 20rpx;
  width: 100%;
}

/* 调试信息样式 */
.debug-info-container {
  margin: 30rpx 0;
  padding: 30rpx;
  background: #f0f8ff;
  border-radius: 10rpx;
  border: 2rpx solid #1890ff;
}

.debug-info-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 20rpx;
}

.debug-info-content {
  margin: 20rpx 0;
}

.debug-item {
  display: flex;
  margin-bottom: 15rpx;
  align-items: flex-start;
}

.debug-label {
  font-size: 28rpx;
  color: #666;
  min-width: 150rpx;
  font-weight: bold;
}

.debug-value {
  font-size: 26rpx;
  color: #333;
  flex: 1;
  word-break: break-all;
  line-height: 1.4;
}

.debug-value.success {
  color: #52c41a;
  font-weight: bold;
}

.debug-value.error {
  color: #f5222d;
  font-weight: bold;
}

.debug-value.code {
  background: #f6f8fa;
  padding: 10rpx;
  border-radius: 4rpx;
  font-family: 'Courier New', monospace;
  color: #d73a49;
}

.clear-debug-btn {
  margin-top: 20rpx;
  width: 100%;
}

.log-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.log-content {
  height: 400rpx;
  padding: 20rpx 30rpx;
}

.log-item {
  margin-bottom: 15rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.log-time {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 5rpx;
}

.log-message {
  font-size: 26rpx;
  line-height: 1.4;
  display: block;
}

.log-message.info {
  color: #1890ff;
}

.log-message.success {
  color: #52c41a;
}

.log-message.warning {
  color: #faad14;
}

.log-message.error {
  color: #f5222d;
}

.no-logs {
  text-align: center;
  padding: 60rpx;
}

.no-logs-text {
  font-size: 28rpx;
  color: #999;
}
</style>

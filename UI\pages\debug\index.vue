<template>
  <view class="debug-container">
    <view class="header">
      <text class="title">🔍 微信登录调试工具</text>
      <text class="subtitle">诊断微信登录和API连接问题</text>
    </view>

    <!-- 快速操作 -->
    <view class="quick-actions">
      <button class="action-btn primary" @click="runFullDiagnosis" :disabled="isRunning">
        {{ isRunning ? '诊断中...' : '🚀 一键诊断' }}
      </button>
      <button class="action-btn secondary" @click="clearLoginData">
        🗑️ 清除登录数据
      </button>
      <button class="action-btn secondary" @click="forceLogin">
        🔐 强制重新登录
      </button>
    </view>

    <!-- 诊断结果 -->
    <view v-if="diagnosisResult" class="diagnosis-result">
      <view class="result-header">
        <text class="result-title">📊 诊断结果</text>
        <text class="result-time">{{ diagnosisResult.timestamp }}</text>
      </view>

      <!-- 登录状态 -->
      <view class="status-section">
        <text class="section-title">🔐 登录状态</text>
        <view class="status-item">
          <text class="status-label">登录状态:</text>
          <text :class="['status-value', diagnosisResult.wechatDebug.loginStatus.isLoggedIn ? 'success' : 'error']">
            {{ diagnosisResult.wechatDebug.loginStatus.isLoggedIn ? '已登录' : '未登录' }}
          </text>
        </view>
        <view class="status-item">
          <text class="status-label">用户信息:</text>
          <text :class="['status-value', diagnosisResult.wechatDebug.loginStatus.hasUserInfo ? 'success' : 'error']">
            {{ diagnosisResult.wechatDebug.loginStatus.hasUserInfo ? '正常' : '缺失' }}
          </text>
        </view>
        <view class="status-item">
          <text class="status-label">用户Token:</text>
          <text :class="['status-value', diagnosisResult.wechatDebug.loginStatus.hasUserToken ? 'success' : 'error']">
            {{ diagnosisResult.wechatDebug.loginStatus.hasUserToken ? '正常' : '缺失' }}
          </text>
        </view>
      </view>

      <!-- 环境信息 -->
      <view class="status-section">
        <text class="section-title">🌐 环境信息</text>
        <view class="status-item">
          <text class="status-label">H5环境:</text>
          <text :class="['status-value', diagnosisResult.wechatDebug.environment.isH5 ? 'success' : 'warning']">
            {{ diagnosisResult.wechatDebug.environment.isH5 ? '是' : '否' }}
          </text>
        </view>
        <view class="status-item">
          <text class="status-label">微信浏览器:</text>
          <text :class="['status-value', diagnosisResult.wechatDebug.environment.isWechatBrowser ? 'success' : 'warning']">
            {{ diagnosisResult.wechatDebug.environment.isWechatBrowser ? '是' : '否' }}
          </text>
        </view>
        <view class="status-item">
          <text class="status-label">配置文件:</text>
          <text :class="['status-value', diagnosisResult.wechatDebug.environment.hasAppConfig ? 'success' : 'error']">
            {{ diagnosisResult.wechatDebug.environment.hasAppConfig ? '已加载' : '未加载' }}
          </text>
        </view>
      </view>

      <!-- API测试结果 -->
      <view class="status-section">
        <text class="section-title">🧪 API测试</text>
        <view class="status-item">
          <text class="status-label">总体状态:</text>
          <text :class="['status-value', diagnosisResult.apiTest.summary.overallStatus === 'PASS' ? 'success' : 'error']">
            {{ diagnosisResult.apiTest.summary.overallStatus }}
          </text>
        </view>
        <view class="status-item">
          <text class="status-label">通过率:</text>
          <text class="status-value">{{ diagnosisResult.apiTest.summary.passRate }}%</text>
        </view>
        <view class="status-item">
          <text class="status-label">API地址:</text>
          <text class="status-value small">{{ diagnosisResult.apiTest.baseUrl }}</text>
        </view>
      </view>

      <!-- 建议 -->
      <view class="recommendations">
        <text class="section-title">💡 问题解决建议</text>
        <view v-for="(recommendation, index) in allRecommendations" :key="index" class="recommendation-item">
          <text class="recommendation-text">{{ recommendation }}</text>
        </view>
      </view>
    </view>

    <!-- 详细日志 -->
    <view v-if="showDetailedLogs" class="detailed-logs">
      <view class="logs-header">
        <text class="logs-title">📝 详细日志</text>
        <button class="toggle-btn" @click="showDetailedLogs = false">收起</button>
      </view>
      <view class="logs-content">
        <text class="log-text">{{ JSON.stringify(diagnosisResult, null, 2) }}</text>
      </view>
    </view>

    <view v-if="diagnosisResult && !showDetailedLogs" class="show-logs">
      <button class="toggle-btn" @click="showDetailedLogs = true">📝 查看详细日志</button>
    </view>

    <!-- 操作说明 -->
    <view class="instructions">
      <text class="instructions-title">📖 使用说明</text>
      <text class="instruction-item">1. 点击"一键诊断"检查所有配置和连接状态</text>
      <text class="instruction-item">2. 如果登录有问题，尝试"清除登录数据"后"强制重新登录"</text>
      <text class="instruction-item">3. 查看建议部分的具体解决方案</text>
      <text class="instruction-item">4. 如果问题持续，请将详细日志发送给技术支持</text>
    </view>
  </view>
</template>

<script>
import wechatDebugger from '@/utils/wechatDebugger.js'
import apiTester from '@/utils/apiTester.js'

export default {
  data() {
    return {
      isRunning: false,
      diagnosisResult: null,
      showDetailedLogs: false
    }
  },

  computed: {
    allRecommendations() {
      if (!this.diagnosisResult) return []
      
      const wechatRecommendations = this.diagnosisResult.wechatDebug.recommendations || []
      const apiRecommendations = apiTester.generateRecommendations(this.diagnosisResult.apiTest) || []
      
      return [...wechatRecommendations, ...apiRecommendations]
    }
  },

  onLoad() {
    // 页面加载时自动运行诊断
    this.runFullDiagnosis()
  },

  methods: {
    async runFullDiagnosis() {
      this.isRunning = true
      
      try {
        uni.showLoading({
          title: '诊断中...',
          mask: true
        })

        // 运行微信调试
        const wechatDebug = await wechatDebugger.generateDebugReport()
        
        // 运行API测试
        const apiTest = await apiTester.runAllTests()

        this.diagnosisResult = {
          timestamp: new Date().toLocaleString(),
          wechatDebug,
          apiTest
        }

        // 打印到控制台
        wechatDebugger.printDebugInfo(wechatDebug)
        apiTester.printTestResults(apiTest)

        uni.hideLoading()
        
        uni.showToast({
          title: '诊断完成',
          icon: 'success',
          duration: 1500
        })

      } catch (error) {
        console.error('诊断失败:', error)
        uni.hideLoading()
        uni.showToast({
          title: '诊断失败',
          icon: 'error',
          duration: 2000
        })
      } finally {
        this.isRunning = false
      }
    },

    clearLoginData() {
      wechatDebugger.clearLoginData()
      uni.showToast({
        title: '登录数据已清除',
        icon: 'success',
        duration: 1500
      })
      
      // 重新运行诊断
      setTimeout(() => {
        this.runFullDiagnosis()
      }, 1000)
    },

    async forceLogin() {
      try {
        uni.showLoading({
          title: '正在登录...',
          mask: true
        })

        const result = await wechatDebugger.forceLogin()
        
        uni.hideLoading()
        
        if (result.success) {
          uni.showToast({
            title: '登录成功',
            icon: 'success',
            duration: 1500
          })
          
          // 重新运行诊断
          setTimeout(() => {
            this.runFullDiagnosis()
          }, 1000)
        } else {
          uni.showToast({
            title: result.message || '登录失败',
            icon: 'error',
            duration: 2000
          })
        }
      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '登录异常',
          icon: 'error',
          duration: 2000
        })
      }
    }
  }
}
</script>

<style scoped>
.debug-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.quick-actions {
  display: flex;
  justify-content: space-around;
  margin-bottom: 30rpx;
  flex-wrap: wrap;
  gap: 20rpx;
}

.action-btn {
  padding: 20rpx 30rpx;
  border-radius: 10rpx;
  border: none;
  font-size: 28rpx;
  flex: 1;
  min-width: 200rpx;
}

.action-btn.primary {
  background-color: #007AFF;
  color: white;
}

.action-btn.secondary {
  background-color: #34C759;
  color: white;
}

.diagnosis-result {
  background-color: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 20rpx;
}

.result-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.result-time {
  font-size: 24rpx;
  color: #999;
}

.status-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.status-label {
  font-size: 28rpx;
  color: #666;
}

.status-value {
  font-size: 28rpx;
  font-weight: bold;
}

.status-value.success {
  color: #34C759;
}

.status-value.error {
  color: #FF3B30;
}

.status-value.warning {
  color: #FF9500;
}

.status-value.small {
  font-size: 24rpx;
  font-weight: normal;
  max-width: 400rpx;
  word-break: break-all;
}

.recommendations {
  background-color: #f8f9fa;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-top: 20rpx;
}

.recommendation-item {
  margin-bottom: 15rpx;
}

.recommendation-text {
  font-size: 26rpx;
  color: #555;
  line-height: 1.5;
  display: block;
}

.detailed-logs {
  background-color: #2d3748;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.logs-title {
  font-size: 30rpx;
  color: #fff;
  font-weight: bold;
}

.toggle-btn {
  background-color: #4A5568;
  color: white;
  border: none;
  padding: 10rpx 20rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
}

.logs-content {
  background-color: #1a202c;
  border-radius: 8rpx;
  padding: 20rpx;
}

.log-text {
  font-family: 'Courier New', monospace;
  font-size: 22rpx;
  color: #e2e8f0;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

.show-logs {
  text-align: center;
  margin-bottom: 30rpx;
}

.instructions {
  background-color: white;
  border-radius: 15rpx;
  padding: 30rpx;
}

.instructions-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.instruction-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  display: block;
  margin-bottom: 15rpx;
  padding-left: 20rpx;
}
</style>

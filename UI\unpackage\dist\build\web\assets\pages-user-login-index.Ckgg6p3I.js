import{_ as e,L as s,a3 as a,M as r,W as i,V as o,J as t,r as n,b as l,c as d,w as c,i as u,d as m,f as g,g as h,o as f,h as b,j as p,t as k,k as L,l as w}from"./index-BcNPWRTq.js";import{a as I}from"./video-user.CxwqIREm.js";import{w as _}from"./wechatUserService.wuq5Y8l4.js";import"./wechat.BxyZ_GJG.js";const v=e({data:()=>({loginForm:{nickname:"",mobile:""},isLoading:!1,errors:{nickname:"",mobile:""}}),computed:{canSubmit(){return this.loginForm.nickname.trim()&&!this.isLoading}},onLoad(e){this.checkExistingLogin(),e.returnUrl&&(this.returnUrl=decodeURIComponent(e.returnUrl))},methods:{validateNickname(){return this.loginForm.nickname.trim()?(this.errors.nickname="",!0):(this.errors.nickname="昵称不能为空",!1)},validateMobile(){const e=this.loginForm.mobile.trim();return e&&!/^1[3-9]\d{9}$/.test(e)?(this.errors.mobile="请输入正确的手机号",!1):(this.errors.mobile="",!0)},clearError(e){this.errors[e]=""},validateForm(){const e=this.validateNickname(),s=this.validateMobile();return e&&s},async handleLogin(){if(this.validateForm()){if(this.canSubmit&&!this.isLoading){this.isLoading=!0;try{const{nickname:e,mobile:a}=this.loginForm,r=await I({nickname:e.trim(),mobile:a.trim()||void 0});r.success&&r.data?(s("userInfo",r.data.userInfo),s("token",r.data.token),this.showToastMessage("登录成功！","success"),await this.delay(1500),this.redirectAfterLogin()):this.showToastMessage(r.msg||"登录失败","error")}catch(e){console.error("Login error:",e),this.showToastMessage("登录失败，请重试","error")}finally{this.isLoading=!1}}}else this.showToastMessage("请检查输入信息","error")},async wechatLogin(){try{this.isLoading=!0;const e=a(),s=e[e.length-1].options||{},o={employeeId:s.employeeId||null,batchId:s.batchId?parseInt(s.batchId):null,sharerId:s.sharerId||null,returnUrl:"/pages/user-login/index"},t=await _.tryWechatAutoLogin(o);if(!t.success||!t.data)throw new Error(t.message||"微信登录失败");this.showToastMessage("微信登录成功！","success"),setTimeout((()=>{s.batchId&&s.videoId?r({url:`/pages/video/index?videoId=${s.videoId}&batchId=${s.batchId}&sharerId=${s.sharerId||""}`}):i({url:"/pages/index/index"})}),1500)}catch(e){console.error("微信登录失败:",e),this.showToastMessage(e.message||"微信登录失败，请重试","error")}finally{this.isLoading=!1}},goToRegister(){o({url:"/pages/register/index"})},checkExistingLogin(){const e=t("userInfo"),s=t("token");e&&s&&this.redirectAfterLogin()},redirectAfterLogin(){this.returnUrl?r({url:this.returnUrl}):i({url:"/pages/index/index"})},showToastMessage(e,s="success"){this.$refs.uToast.show({message:e,type:s,duration:3e3})},delay:e=>new Promise((s=>setTimeout(s,e)))}},[["render",function(e,s,a,r,i,o){const t=w,I=u,_=n(l("u-input"),m),v=n(l("u-button"),g),x=n(l("u-toast"),h);return f(),d(I,{class:"login-container"},{default:c((()=>[b(I,{class:"login-content"},{default:c((()=>[b(I,{class:"header-section"},{default:c((()=>[b(t,{class:"app-title"},{default:c((()=>[p("用户登录")])),_:1}),b(t,{class:"app-subtitle"},{default:c((()=>[p("登录您的账号继续观看")])),_:1})])),_:1}),b(I,{class:"form-container"},{default:c((()=>[b(I,{class:"input-group"},{default:c((()=>[b(t,{class:"input-label"},{default:c((()=>[p("昵称")])),_:1}),b(_,{modelValue:i.loginForm.nickname,"onUpdate:modelValue":s[0]||(s[0]=e=>i.loginForm.nickname=e),placeholder:"请输入您的昵称",border:"surround",clearable:"",error:!!i.errors.nickname,onBlur:o.validateNickname,onInput:s[1]||(s[1]=e=>o.clearError("nickname")),class:"login-input"},null,8,["modelValue","error","onBlur"]),i.errors.nickname?(f(),d(t,{key:0,class:"error-message"},{default:c((()=>[p(k(i.errors.nickname),1)])),_:1})):L("",!0)])),_:1}),b(I,{class:"input-group"},{default:c((()=>[b(t,{class:"input-label"},{default:c((()=>[p("手机号（可选）")])),_:1}),b(_,{modelValue:i.loginForm.mobile,"onUpdate:modelValue":s[2]||(s[2]=e=>i.loginForm.mobile=e),placeholder:"请输入手机号",border:"surround",clearable:"",error:!!i.errors.mobile,onBlur:o.validateMobile,onInput:s[3]||(s[3]=e=>o.clearError("mobile")),class:"login-input"},null,8,["modelValue","error","onBlur"]),i.errors.mobile?(f(),d(t,{key:0,class:"error-message"},{default:c((()=>[p(k(i.errors.mobile),1)])),_:1})):L("",!0)])),_:1}),b(v,{type:"primary",text:i.isLoading?"登录中...":"登录",loading:i.isLoading,disabled:!o.canSubmit||i.isLoading,onClick:o.handleLogin,class:"login-btn"},null,8,["text","loading","disabled","onClick"]),b(I,{class:"register-link-container"},{default:c((()=>[b(t,{class:"register-link-text"},{default:c((()=>[p("还没有账号？")])),_:1}),b(t,{class:"register-link",onClick:o.goToRegister},{default:c((()=>[p("立即注册")])),_:1},8,["onClick"])])),_:1}),b(I,{class:"divider"},{default:c((()=>[b(t,{class:"divider-text"},{default:c((()=>[p("或")])),_:1})])),_:1}),b(v,{type:"success",text:"微信登录",onClick:o.wechatLogin,class:"wechat-btn",disabled:i.isLoading,icon:"weixin"},null,8,["onClick","disabled"])])),_:1}),b(I,{class:"footer"},{default:c((()=>[b(t,{class:"footer-text"},{default:c((()=>[p("© 2024 视频分享系统")])),_:1}),b(t,{class:"footer-version"},{default:c((()=>[p("Version 1.0.0")])),_:1})])),_:1})])),_:1}),b(x,{ref:"uToast"},null,512)])),_:1})}],["__scopeId","data-v-74aeb148"]]);export{v as default};

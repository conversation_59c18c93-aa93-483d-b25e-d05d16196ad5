import{L as e,J as t,K as r}from"./index-BcNPWRTq.js";import{b as i,p as o,g as n}from"./wechat.BxyZ_GJG.js";let s=null;function a(e){s=e}function c(e,t="info"){const r="string"==typeof t?t:"info";console.log(`[WechatWebAuth] [${r.toUpperCase()}] ${e}`),s&&s(`[WechatWebAuth] ${e}`,r)}class d{constructor(){this.config=null,this.initConfig()}initConfig(){c("初始化微信配置..."),"undefined"!=typeof window&&window.APP_CONFIG?(c("从 window.APP_CONFIG 获取微信配置"),this.config=window.APP_CONFIG.wechat,c(`获取到的配置: ${JSON.stringify(this.config)}`)):(c("使用备用微信配置"),this.config={appId:"wx02bacea38f7f3ab5",scope:"snsapi_userinfo",authBaseUrl:"https://open.weixin.qq.com/connect/oauth2/authorize",redirectUri:{development:"http://localhost:5173/#/pages/wechat-callback/index",production:"https://hzlcycc.cn/#/pages/wechat-callback/index"}},c(`备用配置: ${JSON.stringify(this.config)}`)),c("微信配置初始化完成")}isH5Environment(){return c("环境检测: H5环境"),!0}isWechatBrowser(){if("undefined"==typeof window)return!1;return window.navigator.userAgent.toLowerCase().includes("micromessenger")}getRedirectUri(){var e;return"undefined"!=typeof window&&(null==(e=window.APP_CONFIG)?void 0:e.debugMode)?this.config.redirectUri.development:this.config.redirectUri.production}generateState(e={}){return i(e)}parseState(e){return o(e)}async buildAuthUrl(e={}){try{const{state:t=this.generateState(),redirectUri:r=this.getRedirectUri(),scope:i=this.config.scope||"snsapi_userinfo"}=e,o={RedirectUri:r,State:t,Scope:i,InviterId:e.inviterId||""};console.log("调用授权URL接口，参数:",o);const s=await n(o);if(console.log("授权URL接口响应:",s),s.success&&s.data){const e="string"==typeof s.data?s.data:s.data.url||s.data.authUrl;if("string"==typeof e)return e;throw console.error("授权URL格式错误:",s.data),new Error("授权URL格式错误")}throw new Error(s.msg||s.message||"获取授权URL失败")}catch(t){throw console.error("构造授权URL失败:",t),t}}parseCallbackParams(e=""){let t="";t=e||"undefined"==typeof window?e.includes("?")?e.split("?")[1]:e:window.location.search;const r=new URLSearchParams(t);return{code:r.get("code"),state:r.get("state"),error:r.get("error"),error_description:r.get("error_description"),inviterId:r.get("InviterId")}}async startAuth(t={}){try{c(`startAuth 开始，参数: ${JSON.stringify(t)}`);const i=this.isH5Environment();if(c(`检查H5环境: ${i}`),!i)throw new Error("微信网页授权仅支持H5环境");c("验证微信配置...");const o=this.validateConfig();if(!o.valid)throw new Error(`微信配置错误: ${o.errors.join(", ")}`);c("微信配置验证通过");const n="auth_"+Math.random().toString(36).substring(2,15)+Date.now().toString(36),s={timestamp:Date.now(),inviterId:t.inviterId,sharerId:t.inviterId};e(`wechat_auth_session_${n}`,s),c(`授权会话数据已缓存: ${JSON.stringify({authSessionId:n,authSessionData:s})}`);const a={authSessionId:n},d=this.generateState(a);c(`生成状态参数: ${d}`);const h=t.redirectUri||this.getRedirectUri(),l=t.scope||this.config.scope||"snsapi_userinfo";let w;if(c(`授权参数: state=${d}, redirectUri=${h}, scope=${l}`),c("通过后端接口获取授权URL"),w=await this.buildAuthUrl({state:d,redirectUri:h,scope:l,inviterId:t.inviterId}),c(`最终授权URL: ${w}`),c("授权URL类型: "+typeof w),"string"!=typeof w)throw new Error("授权URL格式错误: "+typeof w+", 值: "+JSON.stringify(w));if(!w.startsWith("http"))throw new Error("授权URL格式无效: "+w);if(c("开始微信授权，准备跳转..."),"undefined"==typeof window)throw new Error("window对象不存在，无法执行跳转");{c("执行页面跳转..."),c(`跳转前当前URL: ${window.location.href}`),c("执行页面跳转...");let e=!1;try{window.location.href=w,c("方式1: location.href 已执行"),e=!0}catch(r){c(`方式1失败: ${r.message}`,"warning")}if(!e)try{window.location.assign(w),c("方式2: location.assign 已执行"),e=!0}catch(r){c(`方式2失败: ${r.message}`,"warning")}if(!e)try{window.location.replace(w),c("方式3: location.replace 已执行"),e=!0}catch(r){c(`方式3失败: ${r.message}`,"warning")}if(!e)try{window.open(w,"_self"),c("方式4: window.open 已执行")}catch(r){throw c(`方式4失败: ${r.message}`,"error"),new Error("所有跳转方式都失败了")}c("跳转命令已执行"),setTimeout((()=>{c(`跳转后当前URL: ${window.location.href}`),window.location.href.includes("open.weixin.qq.com")?c("跳转成功！","success"):c("跳转可能失败，URL未改变","warning")}),500)}}catch(i){throw c(`启动微信授权失败: ${i.message}`,"error"),c(`错误堆栈: ${i.stack}`,"error"),i}}handleCallback(e){try{const i=this.parseCallbackParams(e);if(i.error)return{success:!1,error:i.error,errorDescription:i.error_description,message:this.getErrorMessage(i.error)};if(!i.code)return{success:!1,error:"no_code",message:"未获取到授权码"};const o=this.parseState(i.state);let n=null;if(o&&o.authSessionId){const e=`wechat_auth_session_${o.authSessionId}`;n=t(e),n&&(r(e),c(`从缓存中获取授权会话数据: ${JSON.stringify(n)}`))}return{success:!0,code:i.code,state:i.state,stateData:o,authSessionData:n,inviterId:i.inviterId||n&&n.inviterId}}catch(i){return console.error("处理授权回调失败:",i),{success:!1,error:"callback_error",message:"处理授权回调失败: "+i.message}}}getErrorMessage(e){return{access_denied:"用户拒绝授权",invalid_request:"请求参数错误",unauthorized_client:"客户端未授权",unsupported_response_type:"不支持的响应类型",invalid_scope:"无效的授权范围",server_error:"服务器错误",temporarily_unavailable:"服务暂时不可用"}[e]||`授权失败: ${e}`}validateConfig(){const e=[];return this.config.appId||e.push("缺少微信AppId配置"),this.config.authBaseUrl||e.push("缺少微信授权URL配置"),this.config.redirectUri&&(this.config.redirectUri.development||this.config.redirectUri.production)||e.push("缺少回调地址配置"),{valid:0===e.length,errors:e}}}const h=new d,l=()=>h.isH5Environment(),w=()=>h.isWechatBrowser(),u=e=>h.startAuth(e),g=e=>h.handleCallback(e);export{d as WechatWebAuth,h as default,g as handleWechatCallback,l as isH5Environment,w as isWechatBrowser,a as setLogCallback,u as startWechatAuth};

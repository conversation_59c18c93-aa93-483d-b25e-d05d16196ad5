import{_ as a,T as s,o as t,c as e,w as l,h as o,j as c,t as d,an as i,i as n,l as _}from"./index-BcNPWRTq.js";const r=a({name:"PageHeader",props:{title:{type:String,default:""},showBack:{type:Boolean,default:!0}},methods:{goBack(){this.showBack&&s()}}},[["render",function(a,s,r,u,f,h){const p=n,g=_;return t(),e(p,null,{default:l((()=>[o(p,{class:"_page-header"},{default:l((()=>[o(p,{class:"_back-btn",onClick:h.goBack},{default:l((()=>[o(p,{class:"_back-chevron"},{default:l((()=>[c("<")])),_:1})])),_:1},8,["onClick"]),o(p,{class:"_title-container"},{default:l((()=>[o(g,{class:"_page-title"},{default:l((()=>[c(d(r.title),1)])),_:1}),i(a.$slots,"subtitle",{},void 0,!0)])),_:3}),o(p,{class:"_right-slot"},{default:l((()=>[i(a.$slots,"right",{},void 0,!0)])),_:3})])),_:3}),o(p,{class:"_page-header-2"})])),_:3})}],["__scopeId","data-v-36da0fb3"]]);export{r as P};
